RewriteEngine On
RewriteBase /
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{HTTP_HOST} ^([a-z0-9-]+).quifome.com.br [NC]
RewriteRule (.*) index.php?insubdominio=%1&inrouter=$1 [NC,L,QSA]
ErrorDocument 404 /404.php

# php -- B<PERSON>IN cPanel-generated handler, do not edit
# Set the “ea-php74” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php74___lsphp .php .php7 .phtml
</IfModule>
# php -- E<PERSON> cPanel-generated handler, do not edit
