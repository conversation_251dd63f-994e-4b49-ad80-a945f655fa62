
        
.hidden {
    display: none
}

.h-40 {
    height: 47px;
}


.container__payment {
    display: block;
}

.payment-form {
    padding-bottom: 10px;
    margin-right: 15px;
    margin-left: 15px;
    font-family: "Helvetica Neue",Helvetica,sans-serif;
}

.payment-form.dark {
    background-color: #f6f6f6;
}

.payment-form .content {
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.075);
    background-color: white;
}

.payment-form .block-heading {
    padding-top: 40px;
    margin-bottom: 30px;
    text-align: center;
}

.payment-form .block-heading p {
    text-align: center;
    max-width: 420px;
    margin: auto;
    color: RGBA(0,0,0,0.45);
}

.payment-form .block-heading h1,
.payment-form .block-heading h2,
.payment-form .block-heading h3 {
    margin-bottom: 1.2rem;
    color: #009EE3;
}

.payment-form .form-payment {
    background-color: #ffffff;
    padding: 0;
    max-width:650px;
    margin: auto;
}

/* .payment-form .title {
    font-size: 1em;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 0.8em;
    font-weight: 400;
    padding-bottom: 8px;
} */

.payment-form .products {
    background-color: #f7fbff;
    padding: 25px;
}

.payment-form .products .item {
    margin-bottom: 1em;
}

.payment-form .products .item-name {
    font-weight: 500;
    font-size: 0.9em;
}

.payment-form .products .item-description {
    font-size: 0.8em;
    opacity: 0.6;
}

/* .payment-form .products .item p {
    margin-bottom: 0.2em;
} */

.payment-form .products .price {
    float: right;
    font-weight: 500;
    font-size: 0.9em;
}

.payment-form .products .total {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 10px;
    padding-top: 19px;
    font-weight: 500;
    line-height: 1;
}

.payment-form .payment-details {
    padding: 25px 25px 15px;
    height: 100%;
}

/* .payment-form .payment-details label {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #8C8C8C;
    text-transform: uppercase;
} */




.payment-form .payment-details button {
     background-color: #c64c35;
    text-transform: uppercase;
    color: #FFF;
    width: 100%;
    height: 50px;
    border: 1px solid #c64c35;
}

.payment-form .date-separator {
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 5px;
}

.payment-form a, .payment-form a:not([href]) {
    margin: 0;
    padding: 0;
    font-size: 13px;
    color: #009ee3;
    cursor:pointer;
}

.payment-form a:not([href]):hover{
    color: #3483FA;
    cursor:pointer;
}

#loading-message {
    display: none;
    text-align: center;
    font-weight: 700;
}



@media (min-width: 576px) {
    .payment-form .title {
        font-size: 1.2em;
    }

    .payment-form .products {
        padding: 40px;
    }

    .payment-form .products .item-name {
        font-size: 1em;
    }

    .payment-form .products .price {
        font-size: 1em;
    }

    .payment-form .payment-details {
        padding: 40px 40px 30px;
    }

    .payment-form .payment-details button {
        margin-top: 1em;
        margin-bottom: 15px;
    }

  
}

.container__result {
    display: none;
}

#fail-response, #success-response {
    display: none;
}

.validation-error {
    border-color: red;
}

#validation-error-messages p {
    color: red;
}
 