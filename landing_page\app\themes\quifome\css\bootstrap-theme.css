/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
.btn-default,
.btn-primary,
.btn-success,
.btn-info,
.btn-warning,
.btn-danger {
  text-shadow: 0 -1px 0 rgba(0, 0, 0, .2);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 1px rgba(0, 0, 0, .075);
}
.btn-default:active,
.btn-primary:active,
.btn-success:active,
.btn-info:active,
.btn-warning:active,
.btn-danger:active,
.btn-default.active,
.btn-primary.active,
.btn-success.active,
.btn-info.active,
.btn-warning.active,
.btn-danger.active {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.btn-default.disabled,
.btn-primary.disabled,
.btn-success.disabled,
.btn-info.disabled,
.btn-warning.disabled,
.btn-danger.disabled,
.btn-default[disabled],
.btn-primary[disabled],
.btn-success[disabled],
.btn-info[disabled],
.btn-warning[disabled],
.btn-danger[disabled],
fieldset[disabled] .btn-default,
fieldset[disabled] .btn-primary,
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-info,
fieldset[disabled] .btn-warning,
fieldset[disabled] .btn-danger {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.btn-default .badge,
.btn-primary .badge,
.btn-success .badge,
.btn-info .badge,
.btn-warning .badge,
.btn-danger .badge {
  text-shadow: none;
}
.btn:active,
.btn.active {
  background-image: none;
}
.btn-default {
  text-shadow: 0 1px 0 #fff;
  background-image: -webkit-linear-gradient(top, #fff 0%, #e0e0e0 100%);
  background-image:      -o-linear-gradient(top, #fff 0%, #e0e0e0 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#e0e0e0));
  background-image:         linear-gradient(to bottom, #fff 0%, #e0e0e0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe0e0e0', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #dbdbdb;
  border-color: #ccc;
}
.btn-default:hover,
.btn-default:focus {
  background-color: #e0e0e0;
  background-position: 0 -15px;
}
.btn-default:active,
.btn-default.active {
  background-color: #e0e0e0;
  border-color: #dbdbdb;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #e0e0e0;
  background-image: none;
}
.btn-primary {
  background-image: -webkit-linear-gradient(top, #337ab7 0%, #265a88 100%);
  background-image:      -o-linear-gradient(top, #337ab7 0%, #265a88 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#265a88));
  background-image:         linear-gradient(to bottom, #337ab7 0%, #265a88 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff265a88', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #245580;
}
.btn-primary:hover,
.btn-primary:focus {
  background-color: #265a88;
  background-position: 0 -15px;
}
.btn-primary:active,
.btn-primary.active {
  background-color: #265a88;
  border-color: #245580;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #265a88;
  background-image: none;
}
.btn-success {
  background-image: -webkit-linear-gradient(top, #5cb85c 0%, #419641 100%);
  background-image:      -o-linear-gradient(top, #5cb85c 0%, #419641 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#5cb85c), to(#419641));
  background-image:         linear-gradient(to bottom, #5cb85c 0%, #419641 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5cb85c', endColorstr='#ff419641', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #3e8f3e;
}
.btn-success:hover,
.btn-success:focus {
  background-color: #419641;
  background-position: 0 -15px;
}
.btn-success:active,
.btn-success.active {
  background-color: #419641;
  border-color: #3e8f3e;
}
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
  background-color: #419641;
  background-image: none;
}
.btn-info {
  background-image: -webkit-linear-gradient(top, #5bc0de 0%, #2aabd2 100%);
  background-image:      -o-linear-gradient(top, #5bc0de 0%, #2aabd2 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#5bc0de), to(#2aabd2));
  background-image:         linear-gradient(to bottom, #5bc0de 0%, #2aabd2 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5bc0de', endColorstr='#ff2aabd2', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #28a4c9;
}
.btn-info:hover,
.btn-info:focus {
  background-color: #2aabd2;
  background-position: 0 -15px;
}
.btn-info:active,
.btn-info.active {
  background-color: #2aabd2;
  border-color: #28a4c9;
}
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #2aabd2;
  background-image: none;
}
.btn-warning {
  background-image: -webkit-linear-gradient(top, #f0ad4e 0%, #eb9316 100%);
  background-image:      -o-linear-gradient(top, #f0ad4e 0%, #eb9316 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f0ad4e), to(#eb9316));
  background-image:         linear-gradient(to bottom, #f0ad4e 0%, #eb9316 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff0ad4e', endColorstr='#ffeb9316', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #e38d13;
}
.btn-warning:hover,
.btn-warning:focus {
  background-color: #eb9316;
  background-position: 0 -15px;
}
.btn-warning:active,
.btn-warning.active {
  background-color: #eb9316;
  border-color: #e38d13;
}
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #eb9316;
  background-image: none;
}
.btn-danger {
  background-image: -webkit-linear-gradient(top, #d9534f 0%, #c12e2a 100%);
  background-image:      -o-linear-gradient(top, #d9534f 0%, #c12e2a 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#d9534f), to(#c12e2a));
  background-image:         linear-gradient(to bottom, #d9534f 0%, #c12e2a 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9534f', endColorstr='#ffc12e2a', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-color: #b92c28;
}
.btn-danger:hover,
.btn-danger:focus {
  background-color: #c12e2a;
  background-position: 0 -15px;
}
.btn-danger:active,
.btn-danger.active {
  background-color: #c12e2a;
  border-color: #b92c28;
}
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #c12e2a;
  background-image: none;
}
.thumbnail,
.img-thumbnail {
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
          box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  background-color: #e8e8e8;
  background-image: -webkit-linear-gradient(top, #f5f5f5 0%, #e8e8e8 100%);
  background-image:      -o-linear-gradient(top, #f5f5f5 0%, #e8e8e8 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), to(#e8e8e8));
  background-image:         linear-gradient(to bottom, #f5f5f5 0%, #e8e8e8 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff5f5f5', endColorstr='#ffe8e8e8', GradientType=0);
  background-repeat: repeat-x;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  background-color: #2e6da4;
  background-image: -webkit-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
  background-image:      -o-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#2e6da4));
  background-image:         linear-gradient(to bottom, #337ab7 0%, #2e6da4 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff2e6da4', GradientType=0);
  background-repeat: repeat-x;
}
.navbar-default {
  background-image: -webkit-linear-gradient(top, #fff 0%, #f8f8f8 100%);
  background-image:      -o-linear-gradient(top, #fff 0%, #f8f8f8 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fff), to(#f8f8f8));
  background-image:         linear-gradient(to bottom, #fff 0%, #f8f8f8 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff8f8f8', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 5px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, .15), 0 1px 5px rgba(0, 0, 0, .075);
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .active > a {
  background-image: -webkit-linear-gradient(top, #dbdbdb 0%, #e2e2e2 100%);
  background-image:      -o-linear-gradient(top, #dbdbdb 0%, #e2e2e2 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#dbdbdb), to(#e2e2e2));
  background-image:         linear-gradient(to bottom, #dbdbdb 0%, #e2e2e2 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdbdbdb', endColorstr='#ffe2e2e2', GradientType=0);
  background-repeat: repeat-x;
  -webkit-box-shadow: inset 0 3px 9px rgba(0, 0, 0, .075);
          box-shadow: inset 0 3px 9px rgba(0, 0, 0, .075);
}
.navbar-brand,
.navbar-nav > li > a {
  text-shadow: 0 1px 0 rgba(255, 255, 255, .25);
}
.navbar-inverse {
  background-image: -webkit-linear-gradient(top, #3c3c3c 0%, #222 100%);
  background-image:      -o-linear-gradient(top, #3c3c3c 0%, #222 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#3c3c3c), to(#222));
  background-image:         linear-gradient(to bottom, #3c3c3c 0%, #222 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff3c3c3c', endColorstr='#ff222222', GradientType=0);
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-repeat: repeat-x;
  border-radius: 4px;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .active > a {
  background-image: -webkit-linear-gradient(top, #080808 0%, #0f0f0f 100%);
  background-image:      -o-linear-gradient(top, #080808 0%, #0f0f0f 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#080808), to(#0f0f0f));
  background-image:         linear-gradient(to bottom, #080808 0%, #0f0f0f 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff080808', endColorstr='#ff0f0f0f', GradientType=0);
  background-repeat: repeat-x;
  -webkit-box-shadow: inset 0 3px 9px rgba(0, 0, 0, .25);
          box-shadow: inset 0 3px 9px rgba(0, 0, 0, .25);
}
.navbar-inverse .navbar-brand,
.navbar-inverse .navbar-nav > li > a {
  text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
}
.navbar-static-top,
.navbar-fixed-top,
.navbar-fixed-bottom {
  border-radius: 0;
}
@media (max-width: 767px) {
  .navbar .navbar-nav .open .dropdown-menu > .active > a,
  .navbar .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #fff;
    background-image: -webkit-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
    background-image:      -o-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
    background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#2e6da4));
    background-image:         linear-gradient(to bottom, #337ab7 0%, #2e6da4 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff2e6da4', GradientType=0);
    background-repeat: repeat-x;
  }
}
.alert {
  text-shadow: 0 1px 0 rgba(255, 255, 255, .2);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .25), 0 1px 2px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 0 rgba(255, 255, 255, .25), 0 1px 2px rgba(0, 0, 0, .05);
}
.alert-success {
  background-image: -webkit-linear-gradient(top, #dff0d8 0%, #c8e5bc 100%);
  background-image:      -o-linear-gradient(top, #dff0d8 0%, #c8e5bc 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#dff0d8), to(#c8e5bc));
  background-image:         linear-gradient(to bottom, #dff0d8 0%, #c8e5bc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdff0d8', endColorstr='#ffc8e5bc', GradientType=0);
  background-repeat: repeat-x;
  border-color: #b2dba1;
}
.alert-info {
  background-image: -webkit-linear-gradient(top, #d9edf7 0%, #b9def0 100%);
  background-image:      -o-linear-gradient(top, #d9edf7 0%, #b9def0 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#d9edf7), to(#b9def0));
  background-image:         linear-gradient(to bottom, #d9edf7 0%, #b9def0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9edf7', endColorstr='#ffb9def0', GradientType=0);
  background-repeat: repeat-x;
  border-color: #9acfea;
}
.alert-warning {
  background-image: -webkit-linear-gradient(top, #fcf8e3 0%, #f8efc0 100%);
  background-image:      -o-linear-gradient(top, #fcf8e3 0%, #f8efc0 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fcf8e3), to(#f8efc0));
  background-image:         linear-gradient(to bottom, #fcf8e3 0%, #f8efc0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffcf8e3', endColorstr='#fff8efc0', GradientType=0);
  background-repeat: repeat-x;
  border-color: #f5e79e;
}
.alert-danger {
  background-image: -webkit-linear-gradient(top, #f2dede 0%, #e7c3c3 100%);
  background-image:      -o-linear-gradient(top, #f2dede 0%, #e7c3c3 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f2dede), to(#e7c3c3));
  background-image:         linear-gradient(to bottom, #f2dede 0%, #e7c3c3 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2dede', endColorstr='#ffe7c3c3', GradientType=0);
  background-repeat: repeat-x;
  border-color: #dca7a7;
}
.progress {
  background-image: -webkit-linear-gradient(top, #ebebeb 0%, #f5f5f5 100%);
  background-image:      -o-linear-gradient(top, #ebebeb 0%, #f5f5f5 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ebebeb), to(#f5f5f5));
  background-image:         linear-gradient(to bottom, #ebebeb 0%, #f5f5f5 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffebebeb', endColorstr='#fff5f5f5', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar {
  background-image: -webkit-linear-gradient(top, #337ab7 0%, #286090 100%);
  background-image:      -o-linear-gradient(top, #337ab7 0%, #286090 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#286090));
  background-image:         linear-gradient(to bottom, #337ab7 0%, #286090 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff286090', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar-success {
  background-image: -webkit-linear-gradient(top, #5cb85c 0%, #449d44 100%);
  background-image:      -o-linear-gradient(top, #5cb85c 0%, #449d44 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#5cb85c), to(#449d44));
  background-image:         linear-gradient(to bottom, #5cb85c 0%, #449d44 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5cb85c', endColorstr='#ff449d44', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar-info {
  background-image: -webkit-linear-gradient(top, #5bc0de 0%, #31b0d5 100%);
  background-image:      -o-linear-gradient(top, #5bc0de 0%, #31b0d5 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#5bc0de), to(#31b0d5));
  background-image:         linear-gradient(to bottom, #5bc0de 0%, #31b0d5 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5bc0de', endColorstr='#ff31b0d5', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar-warning {
  background-image: -webkit-linear-gradient(top, #f0ad4e 0%, #ec971f 100%);
  background-image:      -o-linear-gradient(top, #f0ad4e 0%, #ec971f 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f0ad4e), to(#ec971f));
  background-image:         linear-gradient(to bottom, #f0ad4e 0%, #ec971f 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff0ad4e', endColorstr='#ffec971f', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar-danger {
  background-image: -webkit-linear-gradient(top, #d9534f 0%, #c9302c 100%);
  background-image:      -o-linear-gradient(top, #d9534f 0%, #c9302c 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#d9534f), to(#c9302c));
  background-image:         linear-gradient(to bottom, #d9534f 0%, #c9302c 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9534f', endColorstr='#ffc9302c', GradientType=0);
  background-repeat: repeat-x;
}
.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}
.list-group {
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
          box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
}
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  text-shadow: 0 -1px 0 #286090;
  background-image: -webkit-linear-gradient(top, #337ab7 0%, #2b669a 100%);
  background-image:      -o-linear-gradient(top, #337ab7 0%, #2b669a 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#2b669a));
  background-image:         linear-gradient(to bottom, #337ab7 0%, #2b669a 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff2b669a', GradientType=0);
  background-repeat: repeat-x;
  border-color: #2b669a;
}
.list-group-item.active .badge,
.list-group-item.active:hover .badge,
.list-group-item.active:focus .badge {
  text-shadow: none;
}
.panel {
  -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
          box-shadow: 0 1px 2px rgba(0, 0, 0, .05);
}
.panel-default > .panel-heading {
  background-image: -webkit-linear-gradient(top, #f5f5f5 0%, #e8e8e8 100%);
  background-image:      -o-linear-gradient(top, #f5f5f5 0%, #e8e8e8 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), to(#e8e8e8));
  background-image:         linear-gradient(to bottom, #f5f5f5 0%, #e8e8e8 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff5f5f5', endColorstr='#ffe8e8e8', GradientType=0);
  background-repeat: repeat-x;
}
.panel-primary > .panel-heading {
  background-image: -webkit-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
  background-image:      -o-linear-gradient(top, #337ab7 0%, #2e6da4 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#337ab7), to(#2e6da4));
  background-image:         linear-gradient(to bottom, #337ab7 0%, #2e6da4 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff337ab7', endColorstr='#ff2e6da4', GradientType=0);
  background-repeat: repeat-x;
}
.panel-success > .panel-heading {
  background-image: -webkit-linear-gradient(top, #dff0d8 0%, #d0e9c6 100%);
  background-image:      -o-linear-gradient(top, #dff0d8 0%, #d0e9c6 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#dff0d8), to(#d0e9c6));
  background-image:         linear-gradient(to bottom, #dff0d8 0%, #d0e9c6 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdff0d8', endColorstr='#ffd0e9c6', GradientType=0);
  background-repeat: repeat-x;
}
.panel-info > .panel-heading {
  background-image: -webkit-linear-gradient(top, #d9edf7 0%, #c4e3f3 100%);
  background-image:      -o-linear-gradient(top, #d9edf7 0%, #c4e3f3 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#d9edf7), to(#c4e3f3));
  background-image:         linear-gradient(to bottom, #d9edf7 0%, #c4e3f3 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9edf7', endColorstr='#ffc4e3f3', GradientType=0);
  background-repeat: repeat-x;
}
.panel-warning > .panel-heading {
  background-image: -webkit-linear-gradient(top, #fcf8e3 0%, #faf2cc 100%);
  background-image:      -o-linear-gradient(top, #fcf8e3 0%, #faf2cc 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fcf8e3), to(#faf2cc));
  background-image:         linear-gradient(to bottom, #fcf8e3 0%, #faf2cc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffcf8e3', endColorstr='#fffaf2cc', GradientType=0);
  background-repeat: repeat-x;
}
.panel-danger > .panel-heading {
  background-image: -webkit-linear-gradient(top, #f2dede 0%, #ebcccc 100%);
  background-image:      -o-linear-gradient(top, #f2dede 0%, #ebcccc 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f2dede), to(#ebcccc));
  background-image:         linear-gradient(to bottom, #f2dede 0%, #ebcccc 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2dede', endColorstr='#ffebcccc', GradientType=0);
  background-repeat: repeat-x;
}
.well {
  background-image: -webkit-linear-gradient(top, #e8e8e8 0%, #f5f5f5 100%);
  background-image:      -o-linear-gradient(top, #e8e8e8 0%, #f5f5f5 100%);
  background-image: -webkit-gradient(linear, left top, left bottom, from(#e8e8e8), to(#f5f5f5));
  background-image:         linear-gradient(to bottom, #e8e8e8 0%, #f5f5f5 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8e8e8', endColorstr='#fff5f5f5', GradientType=0);
  background-repeat: repeat-x;
  border-color: #dcdcdc;
  -webkit-box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05), 0 1px 0 rgba(255, 255, 255, .1);
          box-shadow: inset 0 1px 3px rgba(0, 0, 0, .05), 0 1px 0 rgba(255, 255, 255, .1);
}
/*# sourceMappingURL=bootstrap-theme.css.map */

*,
*:hover,
*:active,
*:focus {
outline: 0 !important;
}

html,
body {
max-width: 100%;
font-family: "Poppins";
overflow-x: hidden;
color: #052336;
}

::-webkit-scrollbar,
scrollbar {
display:none !important;
}

.dontshow {
display: none;
}

.container {
max-width: 1140px;
}

.clear {
clear: both;
} 

.top-mobile {
position: fixed;
width: 100%;
z-index: 10;
padding: 20px 5px 21px 5px;
height: 64px;
background: #261c43;
/*box-shadow: 0 0 10px rgba(0,0,0,.2);*/
}

.top-mobile .brand {
display: block;
margin: -10px auto auto auto;
width: 70px;
height: 70px;
border-radius: 100%;
border: 4px solid #fff;
box-shadow: 0 0 10px rgba(0,0,0,.2);
}

.top-mobile i {
display: block;
color: #fff;
font-size: 27px;
text-align: center;
cursor: pointer;
}

.top-mobile .navbar {
width: 100%;
}

.top-mobile .navbar-collapse {
min-height: 0;
height: 0;
padding: 0 30px 10px 30px;
border-radius: 6px;
background: #fff;
box-shadow: 0 0 5px rgba(0,0,0,.15);
}

.top-mobile .navbar-collapse a,
.top-mobile .navbar-collapse a:hover,
.top-mobile .navbar-collapse a:active {
padding: 17px 16px 14px 16px !important;
width: 100%;
border-radius: 0;
border-bottom: 1px solid rgba(0,0,0,.1);
color: #052336;
border-radius: 30px;
background: transparent;
}

.top-mobile .navbar-collapse li:nth-last-child(-n+2) a {
border-bottom: 0;
}

.top-mobile .navbar-collapse .comece,
.top-mobile .navbar-collapse .comece:hover {
text-align: center;
margin: 0;
border-radius: 30px;
background: #ff6e41 !important;
color: #fff !important;
}

.top-mobile .navbar-collapse.in {
height: auto !important;
}

.top {
position: absolute;
width: 100%;
padding: 10px 0 8px 0;
/*border-bottom: 1px solid rgba(0,0,0,.15);*/
/*background: #f6f8f2;*/
background: rgba(38,28,67,.97);
position: fixed;
z-index: 99;
/*box-shadow: 0 0 10px rgba(0,0,0,.2);*/
}

.top .brand {
display: block;
margin: 11px 0 0 0;
width: 100%;
max-width: 210px;
height: auto;
}

.navbar {
float: right;
margin: 6px 0 0 0;
padding: 0;
}

.navbar a {
display: inline-block;
margin-right: 8px;
padding: 12px 16px 10px 16px !important;
color: #fff;
font-weight: 600;
font-size: 14px;
border-radius: 30px;
transition: 0.3s;
}

.navbar li:last-child a {
margin-right: 0;
}

.navbar a:hover {
background: rgba(0,0,0,.1);
transition: 0.3s;
color: #052336 !important;
}

.navbar .comece {
margin-left: 8px;
padding: 12px 22px 11px 22px !important;
background: #ff6e41;
/*border: 2px solid #ff6e41;*/
color: #052336;
color: rgba(255,255,255,.95);
font-size: 15px;
/*text-shadow: 2px 2px 2px rgba(0,0,0,.25);*/
font-weight: 700;
text-shadow: 1px 1px 1px rgba(0,0,0,.1);
}

.navbar .comece i {
margin-right: 5px;
}

.navbar .comece:hover {
background: #fff;
}

.destaque {
width: 100%;
padding: 180px 0 20px 0;
/*background: url("../img/bg-topo.png") no-repeat bottom center;*/
/*background: #471ca8;*/
background: #271c46 url("../img/destaque.jpg") no-repeat top center fixed;
color: #fff;
}

.destaque .subtitulo {
display: block;
margin: 0;
font-size: 20px;
line-height: 22px;
font-weight: 500;
}

.destaque .titulo {
display: block;
margin: 14px 0 30px 0;
font-size: 40px;
line-height: 40px;
font-weight: 700;
}

.icone {
display: block;
margin-bottom: 16px;
width: 100%;
}

.icone .i {
float: left;
width: 20%;
}

.icone i {
display: block;
width: 40px;
height: 40px;
line-height: 41px;
font-size: 18px;
border-radius: 100%;
text-align: center;
background: #ff6e41;
color: #fff;
}

.icone .textos {
float: right;
width: 79%;
}

.icone-esquerda .icone .i {
float: right;
}

.icone-esquerda .icone .textos {
float: left;
text-align: right;
padding-right: 15px;
}

.icone-direita .icone .i i {
float: right;
}

.icone-direita .icone .textos {
text-align: left;
padding-left: 15px;
}

.icone .textos .icone-titulo {
display: block;
margin: 0 0 5px 0;
font-size: 17px;
line-height: 17px;
font-weight: 700;
}

.icone .textos .icone-texto {
display: block;
font-size: 14px;
line-height: 16px;
font-weight: 400;
}

.destaque .smile {
display: block;
transform: translateX(-16%);
margin: 16px auto auto auto;
width: 100%;
max-width: 250px;
}

.destaque .mockup {
display: block;
margin: -60px auto auto auto;
max-width: 100%;
}

.sessao {
padding: 50px 0 50px 0;
}

.comofunciona {
padding: 50px 0 70px 0;
}

.titulo-generico .subtitulo {
display: block;
text-align: center;
margin: 0;
font-size: 14px;
line-height: 14px;
text-transform: uppercase;
font-weight: 600;
color: #ff6e41;
}

.titulo-generico .titulo {
display: block;
text-align: center;
margin: 12px 0 12px 0;
font-size: 34px;
line-height: 36px;
font-weight: 700;
}

.titulo-generico .texto {
display: block;
margin: auto auto auto auto;
max-width: 500px;
text-align: center;
font-size: 16px;
line-height: 20px;
font-weight: 500;
}

.passo {
margin: 30px 0 30px 0;
}

.passo-cover {
display: flex;
align-items: center;
height: 460px;
}

.passo-img {
display: block;
margin: auto;
}

.passo-img-1 {
width: 100%;
max-width: 235px;
}

.passo-img-2 {
width: 100%;
max-width: 280px;
}

.passo-img-3 {
width: 100%;
max-width: 305px;
}

.passo-numero {
margin: 0 0 16px 0;
}

.passo-numero span {
display: block;
margin: auto;
width: 40px;
height: 40px;
line-height: 42px;
border-radius: 100%;
text-align: center;
background: #ff6e41;
color: #fff;
font-weight: 700;
}

.passo-titulo {
display: block;
margin: 0 0 12px 0;
text-align: center;
font-size: 22px;
line-height: 22px;
font-weight: 700;
}

.passo-texto {
display: block;
margin: 0 0 6px 0;
text-align: center;
font-size: 16px;
line-height: 20px;
font-weight: 500;
}

.passo .passo-textos {
width: 100%;
}

.botao-acao {
display: block;
margin: 20px auto auto auto;
width: 100%;
max-width: 320px;
padding: 20px 20px 16px 16px;
color: #052336;
text-align: center;
font-weight: 700;
font-size: 16px;
border-radius: 30px;
transition: 0.3s;
border: 2px solid #052336;
}

.botao-acao i {
margin-right: 10px;
}

.botao-acao:hover {
background: #ff6e41;
border-color: #ff6e41;
color: #fff;
text-decoration: none;
}

.top .navbar a:hover{
transition: 0.3s;
color: #fff;
}

.demonstracao {
padding: 42px 0 24px 0;
/*background: url("../img/bg-meio.png") no-repeat top center;*/
background: rgba(0,0,0,.03);
background-size: 150% 120%;
}


.icone-esquerda,
.icone-direita {
margin-top: 180px;
}

.demonstracao .demonstracoes {
margin: 20px 0 40px 0;
}

.demos {
}

.carousel-inner {
border-radius: 30px;
}

.demos .item {
padding: 20px 0 20px 0;
}

.demos .phone {
position: absolute;
margin: -25px auto auto auto;
z-index: 2;
width: 100%;
height: 475px;
background: url("../img/phone.png") no-repeat top center;
background-size: 100% auto;
pointer-events: none;
}

.demos .imagem {
margin: auto;
width: 83%;
max-height: 450px;
border-radius: 10px;
overflow: hidden;
}

.demos .imagem img {
display: block;
margin: auto;
width: 100%;
}

.demos .acessar {
position: absolute;
width: 95%;
text-align: center;
margin: -45px auto auto auto;
left: 0;
right: 0;
z-index: 10;
padding: 20px 16px 16px 16px;
background: #261c43;
color: #fff;
font-weight: 600;
font-size: 16px;
border-radius: 30px;
transition: 0.3s;
box-shadow: 0 0 10px rgba(0,0,0,.3);
transition: 0.3s;
}

.demos .acessar:hover {
background: #ff6e41;
transition: 0.3s;
}

.demos .acessar i {
margin-right: 10px;
}

.carousel-control {
display: flex;
align-items: center;
background: transparent !important;
opacity: 1;
}

.carousel-control i {
display: block;
background: #261c43;
border-radius: 100%;
font-size: 20px !important;
width: 40px !important;
height: 40px !important;
line-height: 40px !important;
}

.carousel-esquerda {
margin-left: -5%;
}

.carousel-direita {
margin-right: -5%;
}

.fake-select {
margin: 0 auto 15px auto;
max-width: 70%;
border: 0;
padding: 0;
/*border: 1px solid rgba(0,0,0,.2);*/
border-radius: 0;
background: transparent;
border-bottom: 2px solid rgba(0,0,0,.2);
/*overflow: hidden;*/
}

.fake-select select {
display: block;
-moz-appearance:none;
-webkit-appearance:none;
appearance:none;
padding: 15px 16px 12px 0;
border: 0;
width: 100.3%;
background: transparent;
font-size: 18px;
font-weight: 700;
}

.fake-select i {
position: absolute;
pointer-events: none;
margin: 22px 30px 0 0;
right: 0;
text-align: center;
color: #ff6e41;
transform: translateX(-40px);
}

.precos {
padding: 0 0 60px 0;
background: rgba(0,0,0,.05);
}

.precos .dark {
padding: 40px 0 150px 0;
background: #261c43;
}

.precos .titulo-generico {
color: #fff;
}

.plano {
margin: -70px 0 0 0;
border-radius: 10px;
background: #fff;
padding: 40px 30px;
text-align: center;
box-shadow: 0 0 20px rgba(0,0,0,.2);
min-height: 580px;
}

.plano a {
color: #052336;
text-decoration: none;
}

.plano-destaque {
transform: scale(1.1) translateY(-14px);
}

.plano .titulo {
display: block;
font-weight: 700;
}

.plano .titulo {
display: block;
margin: 0 0 5px 0;
font-weight: 700;
font-size: 24px;
}

.plano .subtitulo {
display: block;
margin: auto;
max-width: 260px;
font-weight: 500;
font-size: 15px;
line-height: 18px;
}

.plano .funcionalidades {
display: block;
margin: 20px 0 0 0;
font-size: 15px;
line-height: 18px;
font-weight: 600;
padding: 14px 12px 12px 12px;
background: rgba(0,0,0,.04);
border-radius: 10px 10px 0 0;
}

.lista-funcionalidades {
}

.lista-funcionalidades span {
display: block;
padding: 10px 0 8px 0;
max-width: 300px;
font-weight: 500;
font-size: 14px;
line-height: 20px;
border-bottom: 1px solid rgba(0,0,0,.05);
}

.lista-funcionalidades span:nth-child(odd) {

}

.precifica {
width: 100%;
margin: 18px 0 20px 0;
}

.precifica .parcelas {
display: block;
width: 100%;
text-align: center;
font-size: 12px;
color: rgba(0,0,0,.7);
text-transform: uppercase;
}

.precifica .mensal {
display: block;
margin: 6px 0 4px 0;
width: 100%;
text-align: center;
font-size: 26px;
font-weight: 700;
}

.precifica .avista {
display: block;
width: 100%;
text-align: center;
font-size: 12px;
color: rgba(0,0,0,.7);
text-transform: uppercase;
}

.plano .testar {
display: block;
border-radius: 30px;
margin: auto auto auto auto;
padding: 12px 12px 11px 12px;
width: 90%;
text-align: center;
font-size: 18px;
font-weight: 700;
background: #ff6e41;
color: #fff;
text-shadow: 1px 1px 1px rgba(0,0,0,.2);
/*box-shadow: 5px 5px 5px rgba(0,0,0,.15);*/
}

.plano-destaque .testar {
padding: 11px 12px 9px 12px;
font-size: 17px;
}

.plano .testar i {
transform: translateY(2px);
margin-left: 6px;
}

.duvidas {
padding: 10px 0 70px 0;
background: rgba(0,0,0,.05);
}

.faq {
margin: 40px 0 0 0;
}

.panel-group {
margin-bottom: 10px;
}

.panel-default {
border: 0;
background: #fff;
box-shadow: 0 0 0 transparent;
}

.panel-default > .panel-heading {
border: 0;
padding: 0;
background: #fff;
}

.panel-default > .panel-heading a {
display: block;
width: 100%;
padding: 26px 26px 20px 26px;
text-decoration: none;
font-weight: 400;

}

.panel-default .panel-title a i {
margin-right: 10px;
transform: translateY(0);
color: rgba(0,0,0,.3);
}

.panel-body {
display: block;
border-top: 1px solid rgba(0,0,0,.15);
width: 100%;
padding: 21px 26px 12px 26px;
text-decoration: none;
color: rgba(0,0,0,.8);
}

.contato {
margin: auto;
padding: 26px 0 10px 0;
max-width: 550px;
align-items: center;
justify-content: center;
}

.contato span {
display: block;
margin: auto auto 10px auto;
text-align: center;
font-size: 16px;
line-height: 20px;
font-weight: 500;
}

.contato a {
width: 100%;
max-width: 100%;
}

.rodape {
width: 100%;
padding: 20px 0 0 0;
background: rgba(0,0,0,.05) url("../img/bg-rodape.png") no-repeat top center;
}

.rodape span {
display: block;
text-align: center;
letter-spacing: 1px;
font-weight: 400;
}

.rodape .smile {
display: block;
margin: auto auto 20px auto;
max-width: 200px;
height: auto;
}

.social {
margin: 6px 0 20px 0;
display: flex;
align-items: center;
justify-content: center;
}

.social a {
display: block;
padding: 0 !important;
margin: 7px;
}

.social i {
color: #052336 !important;
font-size: 26px;
opacity: .8;
}

.social i.lni-envelope {
font-size: 32px;
}

.cresta-whatsapp-chat-box,
.cresta-whatsapp-chat-top-header,
.cresta-whatsapp-to-send .cresta-whatsapp-send {
background: #ff6e41 !important;
}

/* US */

@media (max-width: 320px)  {

}

/* XS */

@media (max-width: 544px)  {

}

/* SMALL */

@media (min-width: 545px) and (max-width: 767px)  {

}

/* MEDIUM */

@media (min-width: 546px) and (max-width: 991px)  {

}

/* LARGE */

@media (min-width: 992px) and (max-width: 1199px)  {

}

/* EXTRA LARGE */

@media (min-width: 1200px) {

}

/* ALL MOBILE */

@media (max-width: 980px) {

  .destaque {
  text-align: center;
  width: 100%;
  padding: 100px 20px 20px 20px;
  background: #471ca8 url("../img/destaque.jpg") no-repeat top center;
  background-size: auto 100%;
  }

  .destaque .titulo {
  display: block;
  margin: 14px 0 20px 0;
  font-size: 30px;
  line-height: 32px;
  }

  .icone .i {
  width: 100%;
  }

  .icone .i i {
  display: block;
  margin: auto auto 8px auto;
  }

  .icone .textos {
  width: 100%;
  }

  .icone .textos .icone-titulo {
  font-size: 16px;
  line-height: 16px;
  }

  .destaque .smile {
  margin: 10px auto auto auto;
  max-width: 180px;
  transform: none;
  }

  .destaque .mockup {
  margin: 40px 0 0 0;
  max-width: 100%;
  }

  .comofunciona {
  margin: 0 0 0 0;
  padding: 30px 10px 30px 10px;
  }

  .titulo-generico .titulo {
  font-size: 28px;
  line-height: 32px;
  }

  .passo-img-1,
  .passo-img-2,
  .passo-img-3 {
  width: 100%;
  max-width: 75%;
  height: auto;
  }

  .passo-cover {
  height: auto;
  margin-bottom: 16px;
  }

  .passo .passo-textos {
  margin-bottom: 34px;
  }

  .comofunciona .botao-acao {
  margin-top: -20px !important;
  }

  .demonstracao {
  margin: 20px 0 0 0;
  padding: 30px 0 20px 0;
  background: rgba(0,0,0,.03);
  background-size: auto 150%;
  }

  .icone-esquerda, .icone-direita {
  margin-top: 0;
  }

  .icone-esquerda .icone .textos,
  .icone-direita .icone .textos {
  text-align: center;
  padding: 0;
  }

  .demos {
  margin-bottom: 30px;
  }

  .icone-direita .icone .i i {
  float: none;
  }

  .plano-destaque {
  transform: none;
  }

  .plano {
  margin: 0;
  min-height: 0;
  padding-top: 30px;
  }

  #carouselplanos {
  margin-top: -70px;
  }

  .precos .dark {
  padding: 20px 0 90px 0
  }

  .demonstracao .demonstracoes {
  margin: 20px 0 20px 0;
  }

  .precos {
  padding-bottom: 16px;
  }

  .panel-default > .panel-heading a {
  padding: 20px 20px 17px 20px;
  text-align: center;
  }

  .panel-default > .panel-heading i {
  display: none;
  }

  .duvidas {
  padding-bottom: 10px;
  }

  .rodape .smile {
  max-width: 160px;
  }

  .carousel-esquerda {
  margin-left: -7%;
  }

  .carousel-direita {
  margin-right: -7%;
  }

  .panel-default > .panel-heading + .panel-collapse > .panel-body {
  text-align: center;
  }

}

/* CAIXOTE */

@media (min-width: 981PX) and (max-width: 1080px) {

}