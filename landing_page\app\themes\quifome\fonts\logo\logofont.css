.logofont,
.logofont * {

}

@font-face {
    font-family: '<PERSON><PERSON> Black';
    src: url('<PERSON>roy-Black.woff2') format('woff2'),
        url('<PERSON>roy-Black.woff') format('woff'),
        url('<PERSON>roy-Black.svg#<PERSON>roy-Black') format('svg');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Gilroy Extrabold';
    src: url('Gilroy-ExtraBold.woff2') format('woff2'),
        url('Gilroy-ExtraBold.woff') format('woff'),
        url('Gilroy-ExtraBold.svg#Gilroy-ExtraBold') format('svg');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

.logofont {
font-family: 'Gilroy Black' !important;
font-size: 44px;
line-height: 44px;
color: rgba(255,255,255,.9);
}

.logofont span {
display: block;
transform: translateY(3px);
}

.logofont-dark span {
display: block;
transform: translateY(-2px);
font-family: '<PERSON><PERSON> Black' !important;
color: #261c43 !important;
font-size: 46px !important;
line-height: 46px !important;
}

.logofont strong {
color: #ff6e41;
}

.logofont-mobile span {
display: block;
text-align: center;
transform: translateY(-3px);
font-family: 'Gilroy Black' !important;
font-size: 35px !important;
line-height: 30px !important;
}

.logofont-login span {
display: block;
text-align: center;
margin: 6px 0 0 0;
font-family: 'Gilroy Black' !important;
font-size: 46px !important;
line-height: 46px !important;
}

.logofont-city span {
display: block;
text-align: center;
margin: -4px 0 0 0;
font-family: 'Gilroy Black' !important;
font-size: 38px !important;
line-height: 30px !important;
}