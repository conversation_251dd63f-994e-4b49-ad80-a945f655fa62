<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'brunopazz/getnet-sdk' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brunopazz/getnet-sdk',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'reference' => 'a1994f92febadef14e3e9d76ca89ed681202cd0d',
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.13.2',
            'version' => '1.13.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'reference' => '5b668aef16090008790395c02c893b1ba13f7e08',
            'dev_requirement' => false,
        ),
        'doctrine/collections' => array(
            'pretty_version' => '1.6.8',
            'version' => '1.6.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/collections',
            'aliases' => array(),
            'reference' => '1958a744696c6bb3bb0d28db2611dc11610e78af',
            'dev_requirement' => false,
        ),
        'doctrine/common' => array(
            'pretty_version' => '3.3.0',
            'version' => '3.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/common',
            'aliases' => array(),
            'reference' => 'c824e95d4c83b7102d8bc60595445a6f7d540f96',
            'dev_requirement' => false,
        ),
        'doctrine/event-manager' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/event-manager',
            'aliases' => array(),
            'reference' => '41370af6a30faa9dc0368c4a6814d596e81aba7f',
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'reference' => 'c268e882d4dbdd85e36e4ad69e02dc284f89d229',
            'dev_requirement' => false,
        ),
        'doctrine/persistence' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/persistence',
            'aliases' => array(),
            'reference' => '25ec98a8cbd1f850e60fdb62c0ef77c162da8704',
            'dev_requirement' => false,
        ),
        'edson-nascimento/getnet-php' => array(
            'pretty_version' => 'v1.0.7',
            'version' => '1.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../edson-nascimento/getnet-php',
            'aliases' => array(),
            'reference' => '47ea6afd7f40e0babd4f2fe0b4e8b90e8993ddf6',
            'dev_requirement' => false,
        ),
        'mercadopago/dx-php' => array(
            'pretty_version' => '2.4.7',
            'version' => '2.4.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mercadopago/dx-php',
            'aliases' => array(),
            'reference' => '03ced401f8646e6740e2cb394eef6869cd73a68f',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
    ),
);
