{"name": "doctrine/collections", "type": "library", "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "keywords": ["php", "collections", "array", "iterators"], "homepage": "https://www.doctrine-project.org/projects/collections.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^0.12", "vimeo/psalm": "^4.2.1"}, "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests/Doctrine/Tests"}}}