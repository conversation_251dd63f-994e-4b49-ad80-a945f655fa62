parameters:
    level: 3
    paths:
        - lib
    ignoreErrors:
        # Making classes final as suggested would be a BC-break
        -
            message: '~Unsafe usage of new static\(\)\.~'
            paths:
                - 'lib/Doctrine/Common/Collections/ArrayCollection.php'
                - 'lib/Doctrine/Common/Collections/Criteria.php'
        -
            message: '~Array \(array\<TKey of \(int\|string\), T\>\) does not accept key int\.~'
            path: 'lib/Doctrine/Common/Collections/ArrayCollection.php'

        # This class is new in PHP 8.1 and PHPStan does not know it yet.
        - '/Attribute class ReturnTypeWillChange does not exist./'
