<?xml version="1.0"?>
<psalm
    errorLevel="8"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    phpVersion="8.1"
>
    <projectFiles>
        <directory name="lib/Doctrine/Common" />
        <ignoreFiles>
            <!-- Remove ProxyGeneratorTest once Psalm supports native intersection https://github.com/vimeo/psalm/issues/6413 -->
            <file name="lib/Doctrine/Common/Proxy/ProxyGenerator.php" />
            <file name="tests/Doctrine/Tests/Common/Proxy/ProxyGeneratorTest.php" />
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>
</psalm>
