{"name": "doctrine/event-manager", "type": "library", "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "keywords": ["events", "event", "event dispatcher", "event manager", "event system"], "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "config": {"sort-packages": true}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests/Doctrine/Tests"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}