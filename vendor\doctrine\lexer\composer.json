{"name": "doctrine/lexer", "type": "library", "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "keywords": ["php", "parser", "lexer", "annotations", "doc<PERSON>"], "homepage": "https://www.doctrine-project.org/projects/lexer.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests/Doctrine"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true}}