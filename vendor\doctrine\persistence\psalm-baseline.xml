<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="4.22.0@fc2c6ab4d5fa5d644d8617089f012f3bb84b8703">
  <file src="tests/Persistence/Mapping/ColocatedMappingDriverTest.php">
    <UndefinedInterfaceMethod occurrences="9">
      <code>addExcludePaths</code>
      <code>addPaths</code>
      <code>getExcludePaths</code>
      <code>getExcludePaths</code>
      <code>getFileExtension</code>
      <code>getFileExtension</code>
      <code>getPaths</code>
      <code>getPaths</code>
      <code>setFileExtension</code>
    </UndefinedInterfaceMethod>
  </file>
</files>
