name: <PERSON>HP Composer

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '7.3'
        extensions: mbstring, intl
        ini-values: post_max_size=256M, short_open_tag=On
        coverage: xdebug    
        tools: php-cs-fixer, phpunit

    - name: Validate composer.json and composer.lock
      run: composer validate
      
    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v2
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-

    - name: Install dependencies
      if: steps.composer-cache.outputs.cache-hit != 'true'
      run: composer install --prefer-dist --no-progress --no-suggest

    - name: Run test suite
      run: composer run-script test
      env:
        ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}
        USER_EMAIL: ${{ secrets.USER_EMAIL }}
        CLIENT_ID: ${{ secrets.CLIENT_ID }}
        CLIENT_SECRET: ${{ secrets.CLIENT_SECRET }}
