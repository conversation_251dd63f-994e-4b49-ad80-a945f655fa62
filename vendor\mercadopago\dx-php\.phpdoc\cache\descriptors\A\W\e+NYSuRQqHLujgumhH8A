1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-b381f4dd858f97bd5ab40ad064478048
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"4e3d9a12d97952a9b7c7b85771343c8c";s:7:" * path";s:40:"src/MercadoPago/Entities/Preapproval.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"\MercadoPago";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:12:"\MercadoPago";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"MercadoPago";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:24:"\MercadoPago\Preapproval";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:19:"\MercadoPago\Entity";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"Entity";}s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:15:{s:2:"id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:29:"\MercadoPago\Preapproval::$id";s:36:" phpDocumentor\Reflection\Fqsen name";s:2:"id";}s:7:" * name";s:2:"id";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:2:"id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:27;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Integer":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"payer_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\Preapproval::$payer_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"payer_id";}s:7:" * name";s:8:"payer_id";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:8:"payer_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:34;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Integer":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"payer_email";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:38:"\MercadoPago\Preapproval::$payer_email";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"payer_email";}s:7:" * name";s:11:"payer_email";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:11:"payer_email";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:41;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"back_url";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\Preapproval::$back_url";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"back_url";}s:7:" * name";s:8:"back_url";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:8:"back_url";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:48;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"collector_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:39:"\MercadoPago\Preapproval::$collector_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"collector_id";}s:7:" * name";s:12:"collector_id";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:12:"collector_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:55;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Integer":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:14:"application_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:41:"\MercadoPago\Preapproval::$application_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:14:"application_id";}s:7:" * name";s:14:"application_id";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:14:"application_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:62;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:6:"status";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\Preapproval::$status";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"status";}s:7:" * name";s:6:"status";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:6:"status";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:69;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:14:"auto_recurring";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:41:"\MercadoPago\Preapproval::$auto_recurring";s:36:" phpDocumentor\Reflection\Fqsen name";s:14:"auto_recurring";}s:7:" * name";s:14:"auto_recurring";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:14:"auto_recurring";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:76;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Boolean":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:10:"init_point";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:37:"\MercadoPago\Preapproval::$init_point";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"init_point";}s:7:" * name";s:10:"init_point";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:10:"init_point";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:83;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:18:"sandbox_init_point";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:45:"\MercadoPago\Preapproval::$sandbox_init_point";s:36:" phpDocumentor\Reflection\Fqsen name";s:18:"sandbox_init_point";}s:7:" * name";s:18:"sandbox_init_point";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:18:"sandbox_init_point";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:90;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:6:"reason";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\Preapproval::$reason";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"reason";}s:7:" * name";s:6:"reason";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:6:"reason";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:97;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:18:"external_reference";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:45:"\MercadoPago\Preapproval::$external_reference";s:36:" phpDocumentor\Reflection\Fqsen name";s:18:"external_reference";}s:7:" * name";s:18:"external_reference";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:18:"external_reference";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:104;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"date_created";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:39:"\MercadoPago\Preapproval::$date_created";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"date_created";}s:7:" * name";s:12:"date_created";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:12:"date_created";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:111;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:13:"last_modified";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:40:"\MercadoPago\Preapproval::$last_modified";s:36:" phpDocumentor\Reflection\Fqsen name";s:13:"last_modified";}s:7:" * name";s:13:"last_modified";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:13:"last_modified";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:118;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:19:"preapproval_plan_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:46:"\MercadoPago\Preapproval::$preapproval_plan_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:19:"preapproval_plan_id";}s:7:" * name";s:19:"preapproval_plan_id";s:12:" * namespace";s:24:"\MercadoPago\Preapproval";s:10:" * package";N;s:10:" * summary";s:19:"preapproval_plan_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:125;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:24:"\MercadoPago\Preapproval";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"Preapproval";}s:7:" * name";s:11:"Preapproval";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";s:0:"";s:10:" * summary";s:17:"preapproval class";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";r:1;s:7:" * line";i:19;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:10:"RestMethod";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:4:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:44:"(resource="/preapproval/:id", method="read")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:1;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:49:"(resource="/preapproval/search", method="search")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:2;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:43:"(resource="/preapproval/", method="create")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:3;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:46:"(resource="/preapproval/:id", method="update")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:15:"Preapproval.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:22:"Preapproval class file";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";r:853;}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}