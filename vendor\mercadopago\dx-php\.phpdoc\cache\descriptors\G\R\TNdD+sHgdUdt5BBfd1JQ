1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-1161a5804240a87e6e26e9d81b4550a9
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"04c36055eaab13e12aaaf4deab86a539";s:7:" * path";s:32:"src/MercadoPago/Entities/POS.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"\MercadoPago";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:12:"\MercadoPago";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"MercadoPago";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:16:"\MercadoPago\POS";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:19:"\MercadoPago\Entity";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"Entity";}s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:13:{s:2:"id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:21:"\MercadoPago\POS::$id";s:36:" phpDocumentor\Reflection\Fqsen name";s:2:"id";}s:7:" * name";s:2:"id";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:2:"id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:25;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:4:"name";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\POS::$name";s:36:" phpDocumentor\Reflection\Fqsen name";s:4:"name";}s:7:" * name";s:4:"name";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:4:"name";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:32;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"fixed_amount";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:31:"\MercadoPago\POS::$fixed_amount";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"fixed_amount";}s:7:" * name";s:12:"fixed_amount";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:12:"fixed_amount";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:39;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Float_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"category";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:27:"\MercadoPago\POS::$category";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"category";}s:7:" * name";s:8:"category";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:8:"category";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:46;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Integer":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"store_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:27:"\MercadoPago\POS::$store_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"store_id";}s:7:" * name";s:8:"store_id";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:8:"store_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:53;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"external_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:30:"\MercadoPago\POS::$external_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"external_id";}s:7:" * name";s:11:"external_id";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:18:"external_reference";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:60;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:2:"qr";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:21:"\MercadoPago\POS::$qr";s:36:" phpDocumentor\Reflection\Fqsen name";s:2:"qr";}s:7:" * name";s:2:"qr";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:2:"qr";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:67;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Object_":1:{s:45:" phpDocumentor\Reflection\Types\Object_ fqsen";N;}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:6:"status";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:25:"\MercadoPago\POS::$status";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"status";}s:7:" * name";s:6:"status";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:6:"status";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:74;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"date_created";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:31:"\MercadoPago\POS::$date_created";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"date_created";}s:7:" * name";s:12:"date_created";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:12:"date_created";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:81;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:17:"date_last_updated";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:36:"\MercadoPago\POS::$date_last_updated";s:36:" phpDocumentor\Reflection\Fqsen name";s:17:"date_last_updated";}s:7:" * name";s:17:"date_last_updated";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:17:"date_last_updated";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:88;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:4:"uuid";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\POS::$uuid";s:36:" phpDocumentor\Reflection\Fqsen name";s:4:"uuid";}s:7:" * name";s:4:"uuid";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:4:"uuid";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:95;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:13:"compatible_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\POS::$compatible_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:13:"compatible_id";}s:7:" * name";s:13:"compatible_id";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:13:"compatible_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:102;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\String_":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"user_id";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:26:"\MercadoPago\POS::$user_id";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"user_id";}s:7:" * name";s:7:"user_id";s:12:" * namespace";s:16:"\MercadoPago\POS";s:10:" * package";N;s:10:" * summary";s:7:"user_id";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:109;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:9:"Attribute";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:9:"Attribute";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:2:"()";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Integer":0:{}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:16:"\MercadoPago\POS";s:36:" phpDocumentor\Reflection\Fqsen name";s:3:"POS";}s:7:" * name";s:3:"POS";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";s:0:"";s:10:" * summary";s:9:"POS class";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";r:1;s:7:" * line";i:18;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:3:{s:4:"link";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:43:"phpDocumentor\Descriptor\Tag\LinkDescriptor":4:{s:7:" * link";s:66:"https://www.mercadopago.com/developers/en/reference/pos/_pos/post/";s:7:" * name";s:4:"link";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:25:"Click here for more infos";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:10:"RestMethod";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:5:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:36:"(resource="/pos/:id", method="read")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:1;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:34:"(resource="/pos", method="create")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:2;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:38:"(resource="/pos/:id", method="update")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:3;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:38:"(resource="/pos/:id", method="delete")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:4;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"RestMethod";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:34:"(resource="/pos", method="search")";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:7:"POS.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";r:773;}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}