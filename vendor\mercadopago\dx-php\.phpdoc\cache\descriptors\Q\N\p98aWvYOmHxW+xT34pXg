1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-c1099e8d125578f125a1b2bdd159fc53
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"c341d37f0971a375f4cf06d65b27875d";s:7:" * path";s:40:"src/MercadoPago/Annotation/Attribute.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:23:"\MercadoPago\Annotation";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\Annotation";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"Annotation";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:33:"\MercadoPago\Annotation\Attribute";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:39:"\Doctrine\Common\Annotations\Annotation";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"Annotation";}s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:8:{s:4:"type";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:40:"\MercadoPago\Annotation\Attribute::$type";s:36:" phpDocumentor\Reflection\Fqsen name";s:4:"type";}s:7:" * name";s:4:"type";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:14;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:41:"phpDocumentor\Descriptor\Validation\Error":4:{s:11:" * severity";s:5:"ERROR";s:7:" * code";s:72:"Tag "var" with body "@var" has error Expected a different value than "".";s:7:" * line";i:0;s:10:" * context";a:0:{}}}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"required";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";s:5:"false";s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:44:"\MercadoPago\Annotation\Attribute::$required";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"required";}s:7:" * name";s:8:"required";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:19;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:41:"phpDocumentor\Descriptor\Validation\Error":4:{s:11:" * severity";s:5:"ERROR";s:7:" * code";s:72:"Tag "var" with body "@var" has error Expected a different value than "".";s:7:" * line";i:0;s:10:" * context";a:0:{}}}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:9:"serialize";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";s:4:"true";s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:45:"\MercadoPago\Annotation\Attribute::$serialize";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"serialize";}s:7:" * name";s:9:"serialize";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:20;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"readOnly";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:44:"\MercadoPago\Annotation\Attribute::$readOnly";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"readOnly";}s:7:" * name";s:8:"readOnly";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:21;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:10:"primaryKey";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:46:"\MercadoPago\Annotation\Attribute::$primaryKey";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"primaryKey";}s:7:" * name";s:10:"primaryKey";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:22;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"idempotency";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:47:"\MercadoPago\Annotation\Attribute::$idempotency";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"idempotency";}s:7:" * name";s:11:"idempotency";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:23;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"defaultValue";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:48:"\MercadoPago\Annotation\Attribute::$defaultValue";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"defaultValue";}s:7:" * name";s:12:"defaultValue";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:24;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:9:"maxLength";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:45:"\MercadoPago\Annotation\Attribute::$maxLength";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"maxLength";}s:7:" * name";s:9:"maxLength";s:12:" * namespace";s:33:"\MercadoPago\Annotation\Attribute";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:25;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\Annotation\Attribute";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"Attribute";}s:7:" * name";s:9:"Attribute";s:12:" * namespace";s:23:"\MercadoPago\Annotation";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";r:1;s:7:" * line";i:9;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:10:"Annotation";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:10:"Annotation";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:13:"Attribute.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";r:285;}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}