1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-442a1fafa5de6ae9d0a4c8dc006e9d85
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"98b0a23a3595cbec0b69c75bd67ee345";s:7:" * path";s:23:"src/MercadoPago/SDK.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"\MercadoPago";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:12:"\MercadoPago";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"MercadoPago";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:16:"\MercadoPago\SDK";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";N;s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:3:{s:7:"_config";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:1;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:26:"\MercadoPago\SDK::$_config";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"_config";}s:7:" * name";s:7:"_config";s:12:" * namespace";s:16:"\MercadoPago\SDK";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:15;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Object_":1:{s:45:" phpDocumentor\Reflection\Types\Object_ fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:19:"\MercadoPago\Config";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"Config";}}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"_manager";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:1;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:27:"\MercadoPago\SDK::$_manager";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"_manager";}s:7:" * name";s:8:"_manager";s:12:" * namespace";s:16:"\MercadoPago\SDK";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:19;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Object_":1:{s:45:" phpDocumentor\Reflection\Types\Object_ fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:20:"\MercadoPago\Manager";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"Manager";}}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"_restClient";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";N;s:9:" * static";b:1;s:13:" * visibility";s:9:"protected";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:30:"\MercadoPago\SDK::$_restClient";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"_restClient";}s:7:" * name";s:11:"_restClient";s:12:" * namespace";s:16:"\MercadoPago\SDK";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:24;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:41:"phpDocumentor\Descriptor\Validation\Error":4:{s:11:" * severity";s:5:"ERROR";s:7:" * code";s:72:"Tag "var" with body "@var" has error Expected a different value than "".";s:7:" * line";i:0;s:10:" * context";a:0:{}}}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:25:{s:10:"initialize";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:30:"\MercadoPago\SDK::initialize()";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"initialize";}s:7:" * name";s:10:"initialize";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:27:"MercadoPagoSdk constructor.";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:29;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:14:"setAccessToken";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"access_token";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:187;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:12:"access_token";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:41;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\SDK::setAccessToken()";s:36:" phpDocumentor\Reflection\Fqsen name";s:14:"setAccessToken";}s:7:" * name";s:14:"setAccessToken";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:26:"Set Access Token for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:41;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:14:"getAccessToken";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\SDK::getAccessToken()";s:36:" phpDocumentor\Reflection\Fqsen name";s:14:"getAccessToken";}s:7:" * name";s:14:"getAccessToken";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:49;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"getCountryId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\SDK::getCountryId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"getCountryId";}s:7:" * name";s:12:"getCountryId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:53;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:16:"cleanCredentials";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:36:"\MercadoPago\SDK::cleanCredentials()";s:36:" phpDocumentor\Reflection\Fqsen name";s:16:"cleanCredentials";}s:7:" * name";s:16:"cleanCredentials";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:57;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:22:"setMultipleCredentials";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"array";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:308;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:5:"array";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:65;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:42:"\MercadoPago\SDK::setMultipleCredentials()";s:36:" phpDocumentor\Reflection\Fqsen name";s:22:"setMultipleCredentials";}s:7:" * name";s:22:"setMultipleCredentials";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:65;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"setClientId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:9:"client_id";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:353;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:9:"client_id";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:74;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:31:"\MercadoPago\SDK::setClientId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"setClientId";}s:7:" * name";s:11:"setClientId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:29:"Set Access ClientId for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:74;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"getClientId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:31:"\MercadoPago\SDK::getClientId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"getClientId";}s:7:" * name";s:11:"getClientId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:81;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:15:"setClientSecret";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:13:"client_secret";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:426;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:13:"client_secret";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:88;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\SDK::setClientSecret()";s:36:" phpDocumentor\Reflection\Fqsen name";s:15:"setClientSecret";}s:7:" * name";s:15:"setClientSecret";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:33:"Set Access ClientSecret for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:88;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:15:"getClientSecret";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\SDK::getClientSecret()";s:36:" phpDocumentor\Reflection\Fqsen name";s:15:"getClientSecret";}s:7:" * name";s:15:"getClientSecret";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:95;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"setPublicKey";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:10:"public_key";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:499;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:10:"public_key";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:102;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\SDK::setPublicKey()";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"setPublicKey";}s:7:" * name";s:12:"setPublicKey";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:33:"Set Access ClientSecret for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:102;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"getPublicKey";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\SDK::getPublicKey()";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"getPublicKey";}s:7:" * name";s:12:"getPublicKey";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:106;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:9:"configure";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:4:"data";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:572;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";s:2:"[]";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:4:"data";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:110;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:29:"\MercadoPago\SDK::configure()";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"configure";}s:7:" * name";s:9:"configure";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:110;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:6:"config";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:26:"\MercadoPago\SDK::config()";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"config";}s:7:" * name";s:6:"config";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:119;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Object_":1:{s:45:" phpDocumentor\Reflection\Types\Object_ fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:19:"\MercadoPago\Config";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"Config";}}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:22:"addCustomTrackingParam";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"key";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:660;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"key";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:124;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:5:"value";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:660;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:5:"value";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:124;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:42:"\MercadoPago\SDK::addCustomTrackingParam()";s:36:" phpDocumentor\Reflection\Fqsen name";s:22:"addCustomTrackingParam";}s:7:" * name";s:22:"addCustomTrackingParam";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:124;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:3:"get";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"uri";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:724;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"uri";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:132;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"options";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:724;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";s:2:"[]";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:7:"options";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:132;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\SDK::get()";s:36:" phpDocumentor\Reflection\Fqsen name";s:3:"get";}s:7:" * name";s:3:"get";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:132;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:4:"post";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"uri";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:788;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"uri";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:137;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"options";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:788;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";s:2:"[]";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:7:"options";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:137;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:24:"\MercadoPago\SDK::post()";s:36:" phpDocumentor\Reflection\Fqsen name";s:4:"post";}s:7:" * name";s:4:"post";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:137;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:3:"put";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"uri";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:852;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"uri";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:142;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"options";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:852;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";s:2:"[]";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:7:"options";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:142;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\SDK::put()";s:36:" phpDocumentor\Reflection\Fqsen name";s:3:"put";}s:7:" * name";s:3:"put";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:142;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:6:"delete";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"uri";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:916;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"uri";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:147;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"options";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:916;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";s:2:"[]";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:7:"options";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:147;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:26:"\MercadoPago\SDK::delete()";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"delete";}s:7:" * name";s:6:"delete";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:147;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:13:"setPlatformId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:11:"platform_id";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:980;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:11:"platform_id";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:155;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\SDK::setPlatformId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:13:"setPlatformId";}s:7:" * name";s:13:"setPlatformId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:25:"Set Platform Id for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:155;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:13:"getPlatformId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\SDK::getPlatformId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:13:"getPlatformId";}s:7:" * name";s:13:"getPlatformId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:163;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:16:"setCorporationId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:14:"corporation_id";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:1053;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:14:"corporation_id";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:170;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:36:"\MercadoPago\SDK::setCorporationId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:16:"setCorporationId";}s:7:" * name";s:16:"setCorporationId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:28:"Set Corporation Id for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:170;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:16:"getCorporationId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:36:"\MercadoPago\SDK::getCorporationId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:16:"getCorporationId";}s:7:" * name";s:16:"getCorporationId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:178;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:15:"setIntegratorId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:13:"integrator_id";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:1126;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:13:"integrator_id";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:185;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\SDK::setIntegratorId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:15:"setIntegratorId";}s:7:" * name";s:15:"setIntegratorId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:27:"Set Integrator Id for SDK .";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:185;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:15:"getIntegratorId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\SDK::getIntegratorId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:15:"getIntegratorId";}s:7:" * name";s:15:"getIntegratorId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:193;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:16:"\MercadoPago\SDK";s:36:" phpDocumentor\Reflection\Fqsen name";s:3:"SDK";}s:7:" * name";s:3:"SDK";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";s:11:"MercadoPago";s:10:" * summary";s:32:"MercadoPagoSdk Class Doc Comment";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";r:1;s:7:" * line";i:9;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:11:"MercadoPago";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:7:"SDK.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}