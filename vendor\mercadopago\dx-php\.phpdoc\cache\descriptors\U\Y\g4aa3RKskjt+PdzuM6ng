1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-db9bce92b753259b6d3e1511a3697c7f
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"c08f38bea0b68a89c79ab805eafff644";s:7:" * path";s:26:"src/MercadoPago/Config.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"\MercadoPago";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:12:"\MercadoPago";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"MercadoPago";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:19:"\MercadoPago\Config";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\Config\AbstractConfig";s:36:" phpDocumentor\Reflection\Fqsen name";s:14:"AbstractConfig";}s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:21:"_supportedFileParsers";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";s:54:"['MercadoPago\Config\Json', 'MercadoPago\Config\Yaml']";s:9:" * static";b:0;s:13:" * visibility";s:7:"private";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:43:"\MercadoPago\Config::$_supportedFileParsers";s:36:" phpDocumentor\Reflection\Fqsen name";s:21:"_supportedFileParsers";}s:7:" * name";s:21:"_supportedFileParsers";s:12:" * namespace";s:19:"\MercadoPago\Config";s:10:" * package";N;s:10:" * summary";s:17:"Available parsers";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:18;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:3:"var";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:42:"phpDocumentor\Descriptor\Tag\VarDescriptor":5:{s:15:" * variableName";s:0:"";s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Array_":3:{s:12:" * valueType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * keyType";N;s:17:" * defaultKeyType";O:39:"phpDocumentor\Reflection\Types\Compound":2:{s:52:" phpDocumentor\Reflection\Types\AggregatedType types";a:2:{i:0;O:38:"phpDocumentor\Reflection\Types\String_":0:{}i:1;O:38:"phpDocumentor\Reflection\Types\Integer":0:{}}s:52:" phpDocumentor\Reflection\Types\AggregatedType token";s:1:"|";}}s:7:" * name";s:3:"var";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"_restclient";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";s:4:"null";s:9:" * static";b:0;s:13:" * visibility";s:7:"private";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\Config::$_restclient";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"_restclient";}s:7:" * name";s:11:"_restclient";s:12:" * namespace";s:19:"\MercadoPago\Config";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:23;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:9:{s:11:"getDefaults";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:9:"protected";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\Config::getDefaults()";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"getDefaults";}s:7:" * name";s:11:"getDefaults";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:14:"Default values";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:29;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Array_":3:{s:12:" * valueType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * keyType";N;s:17:" * defaultKeyType";O:39:"phpDocumentor\Reflection\Types\Compound":2:{s:52:" phpDocumentor\Reflection\Types\AggregatedType types";a:2:{i:0;O:38:"phpDocumentor\Reflection\Types\String_":0:{}i:1;O:38:"phpDocumentor\Reflection\Types\Integer":0:{}}s:52:" phpDocumentor\Reflection\Types\AggregatedType token";s:1:"|";}}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:4:"load";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:1;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:4:"path";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:149;s:7:" * type";O:36:"phpDocumentor\Reflection\Types\Null_":0:{}s:10:" * default";s:4:"null";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:4:"path";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:18:"Static load method";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:49;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:27:"\MercadoPago\Config::load()";s:36:" phpDocumentor\Reflection\Fqsen name";s:4:"load";}s:7:" * name";s:4:"load";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:49;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:4:"path";s:8:" * types";r:159;s:7:" * name";s:5:"param";s:14:" * description";r:168;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Static_":0:{}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:11:"__construct";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:4:"path";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:221;s:7:" * type";O:36:"phpDocumentor\Reflection\Types\Null_":0:{}s:10:" * default";s:4:"null";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:4:"path";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:60;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:10:"restClient";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:221;s:7:" * type";O:36:"phpDocumentor\Reflection\Types\Null_":0:{}s:10:" * default";s:4:"null";s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:10:"restClient";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:60;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\Config::__construct()";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"__construct";}s:7:" * name";s:11:"__construct";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:19:"Config constructor.";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:60;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{i:0;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:4:"path";s:8:" * types";r:231;s:7:" * name";s:5:"param";s:14:" * description";r:240;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:1;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:10:"restClient";s:8:" * types";r:254;s:7:" * name";s:5:"param";s:14:" * description";r:263;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:10:"_getParser";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:7:"private";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:9:"extension";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:311;s:7:" * type";N;s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:9:"extension";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:33:"Get Parser depending on extension";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:85;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:33:"\MercadoPago\Config::_getParser()";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"_getParser";}s:7:" * name";s:10:"_getParser";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:85;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:3:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:9:"extension";s:8:" * types";N;s:7:" * name";s:5:"param";s:14:" * description";r:330;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:36:"phpDocumentor\Reflection\Types\Null_":0:{}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}s:6:"throws";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ThrowsDescriptor":4:{s:8:" * types";O:38:"phpDocumentor\Reflection\Types\Object_":1:{s:45:" phpDocumentor\Reflection\Types\Object_ fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:10:"\Exception";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"Exception";}}s:7:" * name";s:6:"throws";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:3:"set";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:3:"key";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:398;s:7:" * type";N;s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:3:"key";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:107;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:5:"value";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:398;s:7:" * type";N;s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:5:"value";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:107;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:26:"\MercadoPago\Config::set()";s:36:" phpDocumentor\Reflection\Fqsen name";s:3:"set";}s:7:" * name";s:3:"set";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:16:"Set config value";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:107;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{i:0;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:3:"key";s:8:" * types";N;s:7:" * name";s:5:"param";s:14:" * description";r:417;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}i:1;O:44:"phpDocumentor\Descriptor\Tag\ParamDescriptor":5:{s:15:" * variableName";s:5:"value";s:8:" * types";N;s:7:" * name";s:5:"param";s:14:" * description";r:440;s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:9:"getUserId";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\Config::getUserId()";s:36:" phpDocumentor\Reflection\Fqsen name";s:9:"getUserId";}s:7:" * name";s:9:"getUserId";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:140;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"getToken";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:31:"\MercadoPago\Config::getToken()";s:36:" phpDocumentor\Reflection\Fqsen name";s:8:"getToken";}s:7:" * name";s:8:"getToken";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:32:"Obtain token with key and secret";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:155;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:12:"refreshToken";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:35:"\MercadoPago\Config::refreshToken()";s:36:" phpDocumentor\Reflection\Fqsen name";s:12:"refreshToken";}s:7:" * name";s:12:"refreshToken";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:13:"Refresh token";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";N;s:7:" * line";i:173;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"return";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:45:"phpDocumentor\Descriptor\Tag\ReturnDescriptor":4:{s:8:" * types";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:7:" * name";s:6:"return";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:55:"//TODO check valid response with production credentials";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"getData";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:30:"\MercadoPago\Config::getData()";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"getData";}s:7:" * name";s:7:"getData";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:190;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:19:"\MercadoPago\Config";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"Config";}s:7:" * name";s:6:"Config";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";s:11:"MercadoPago";s:10:" * summary";s:24:"Config Class Doc Comment";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:17:" * fileDescriptor";r:1;s:7:" * line";i:11;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:11:"MercadoPago";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:10:"Config.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}