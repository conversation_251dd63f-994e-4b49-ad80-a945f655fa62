1640909181
LpGsUSVZxt%3AphpDocumentor-projectDescriptor-files-98638b5381e3dfb983416eb83be74361
O:39:"phpDocumentor\Descriptor\FileDescriptor":22:{s:7:" * hash";s:32:"f4acdccdc2850457d7f6515349367dbb";s:7:" * path";s:20:"tests/FakeApiHub.php";s:9:" * source";N;s:19:" * namespaceAliases";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:12:"\MercadoPago";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:12:"\MercadoPago";s:36:" phpDocumentor\Reflection\Fqsen name";s:11:"MercadoPago";}}}s:11:" * includes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:12:" * functions";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * classes";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:23:"\MercadoPago\FakeApiHub";O:40:"phpDocumentor\Descriptor\ClassDescriptor":19:{s:9:" * parent";N;s:13:" * implements";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:11:" * abstract";b:0;s:8:" * final";b:0;s:12:" * constants";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:13:" * properties";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:6:"_files";O:43:"phpDocumentor\Descriptor\PropertyDescriptor":18:{s:9:" * parent";r:18;s:7:" * type";N;s:10:" * default";s:231:"['/oauth/token' => 'authorization.json', '/v1/payment_methods' => 'payment_methods.json', '/v1/payments' => 'payment.json', '/dummies' => 'dummies.json', '/v1/dummies/search' => 'customer_search.json', '/dummy/:id' => 'dummy.json']";s:9:" * static";b:0;s:13:" * visibility";s:7:"private";s:53:" phpDocumentor\Descriptor\PropertyDescriptor readOnly";b:0;s:54:" phpDocumentor\Descriptor\PropertyDescriptor writeOnly";b:0;s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:32:"\MercadoPago\FakeApiHub::$_files";s:36:" phpDocumentor\Reflection\Fqsen name";s:6:"_files";}s:7:" * name";s:6:"_files";s:12:" * namespace";s:23:"\MercadoPago\FakeApiHub";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:5;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:10:" * methods";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:7:"getJson";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:6:"public";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:2:{s:6:"method";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:53;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:6:"method";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:13;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:8:"endPoint";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:53;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:8:"endPoint";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:13;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\FakeApiHub::getJson()";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"getJson";}s:7:" * name";s:7:"getJson";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:13;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}s:7:"getFile";O:41:"phpDocumentor\Descriptor\MethodDescriptor":18:{s:9:" * parent";r:18;s:11:" * abstract";b:0;s:8:" * final";b:0;s:9:" * static";b:0;s:13:" * visibility";s:7:"private";s:12:" * arguments";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:8:"endpoint";O:43:"phpDocumentor\Descriptor\ArgumentDescriptor":16:{s:9:" * method";r:117;s:7:" * type";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:10:" * default";N;s:14:" * byReference";b:0;s:13:" * isVariadic";b:0;s:8:" * fqsen";N;s:7:" * name";s:8:"endpoint";s:12:" * namespace";s:0:"";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:28;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:53:" phpDocumentor\Descriptor\MethodDescriptor returnType";O:37:"phpDocumentor\Reflection\Types\Mixed_":0:{}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:34:"\MercadoPago\FakeApiHub::getFile()";s:36:" phpDocumentor\Reflection\Fqsen name";s:7:"getFile";}s:7:" * name";s:7:"getFile";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";N;s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:28;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:5:"param";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * usedTraits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";O:30:"phpDocumentor\Reflection\Fqsen":2:{s:37:" phpDocumentor\Reflection\Fqsen fqsen";s:23:"\MercadoPago\FakeApiHub";s:36:" phpDocumentor\Reflection\Fqsen name";s:10:"FakeApiHub";}s:7:" * name";s:10:"FakeApiHub";s:12:" * namespace";s:12:"\MercadoPago";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";r:1;s:7:" * line";i:3;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{i:0;O:38:"phpDocumentor\Descriptor\TagDescriptor":3:{s:7:" * name";s:7:"package";s:14:" * description";O:55:"phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor":2:{s:68:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor description";O:45:"phpDocumentor\Reflection\DocBlock\Description":2:{s:59:" phpDocumentor\Reflection\DocBlock\Description bodyTemplate";s:0:"";s:51:" phpDocumentor\Reflection\DocBlock\Description tags";a:0:{}}s:67:" phpDocumentor\Descriptor\DocBlock\DescriptionDescriptor inlineTags";a:0:{}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}}}}}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}}}s:13:" * interfaces";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:9:" * traits";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:10:" * markers";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:8:" * fqsen";N;s:7:" * name";s:14:"FakeApiHub.php";s:12:" * namespace";s:0:"";s:10:" * package";s:0:"";s:10:" * summary";s:0:"";s:14:" * description";N;s:17:" * fileDescriptor";N;s:7:" * line";i:0;s:7:" * tags";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:1:{s:7:"package";r:176;}}s:9:" * errors";O:35:"phpDocumentor\Descriptor\Collection":1:{s:8:" * items";a:0:{}}s:19:" * inheritedElement";N;}