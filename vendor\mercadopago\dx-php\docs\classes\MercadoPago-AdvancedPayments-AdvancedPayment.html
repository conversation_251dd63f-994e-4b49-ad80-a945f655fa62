<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Documentation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/js/all.min.js" integrity="sha256-0vuk8LXoyrmCjp1f0O300qo1M75ZQyhH9X3J6d+scmk=" crossorigin="anonymous"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -four phpdocumentor-sidebar">
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                <h3 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/default.html"><abbr title="\">Global</abbr></a></h3>
                                        <h4 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/mercadopago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h4>
                <ul class="phpdocumentor-list">
                                            <li><a href="namespaces/mercadopago-annotation.html"><abbr title="\MercadoPago\Annotation">Annotation</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-advancedpayments.html"><abbr title="\MercadoPago\AdvancedPayments">AdvancedPayments</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                                    </ul>
                        </section>

        <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                    <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/Default.html"><abbr title="\Default">Default</abbr></a></h3>
                                <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/MercadoPago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h3>
                        <ul class="phpdocumentor-list">
                                    <li><a href="packages/MercadoPago-Config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                    <li><a href="packages/MercadoPago-Http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                            </ul>
                        </section>
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -eight phpdocumentor-content">
                    <ul class="phpdocumentor-breadcrumbs">
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/mercadopago.html">MercadoPago</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/mercadopago-advancedpayments.html">AdvancedPayments</a></li>
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    AdvancedPayment

        <span class="phpdocumentor-element__extends">
        extends <a href="classes/MercadoPago-Entity.html"><abbr title="\MercadoPago\Entity">Entity</abbr></a>
    </span>
    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/Default.html">Default</a></li>
                            </ul>
        </div>
    
    
    </h2>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">19</span>
</aside>

            <p class="phpdocumentor-summary">Advanced Payment class</p>

    <section class="phpdocumentor-description"></section>


    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">link</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                                        <a class="phpdocumentor-tag-link" href="https://www.mercadopago.com/developers/en/reference/advanced_payments/_advanced_payments_id_search/get/"> <p>Click here for more infos</p>
 </a>
                    
                                    </dd>
                                                <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">RestMethod</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(resource=&quot;/v1/advanced_payments&quot;, method=&quot;create&quot;)</p>
</section>

                                    </dd>
                            <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">RestMethod</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(resource=&quot;/v1/advanced_payments/:id&quot;, method=&quot;read&quot;)</p>
</section>

                                    </dd>
                            <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">RestMethod</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(resource=&quot;/v1/advanced_payments/search&quot;, method=&quot;search&quot;)</p>
</section>

                                    </dd>
                            <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">RestMethod</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(resource=&quot;/v1/advanced_payments/:id&quot;, method=&quot;update&quot;)</p>
</section>

                                    </dd>
                            <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">RestMethod</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(resource=&quot;/v1/advanced_payments/:id/refunds&quot;, method=&quot;refund&quot;)</p>
</section>

                                    </dd>
                        </dl>






<h3 id="toc">
    Table of Contents
    <a href="#toc" class="headerlink"><i class="fas fa-link"></i></a>
</h3>

<dl class="phpdocumentor-table-of-contents">
                <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property__custom_headers">$_custom_headers</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property__empty">$_empty</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property__last">$_last</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property__manager">$_manager</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property__pagination_params">$_pagination_params</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_application_id">$application_id</a>
    <span>
                &nbsp;: int            </span>
</dt>
<dd>application_id</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_binary_mode">$binary_mode</a>
    <span>
                &nbsp;: bool            </span>
</dt>
<dd>binary_mode</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_capture">$capture</a>
    <span>
                &nbsp;: bool            </span>
</dt>
<dd>capture</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_description">$description</a>
    <span>
                &nbsp;: string            </span>
</dt>
<dd>description</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_disbursements">$disbursements</a>
    <span>
                &nbsp;: array&lt;string|int, mixed&gt;            </span>
</dt>
<dd>disbursements</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-Entity.html#property_error">$error</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_external_reference">$external_reference</a>
    <span>
                &nbsp;: string            </span>
</dt>
<dd>external_reference</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_id">$id</a>
    <span>
                &nbsp;: int            </span>
</dt>
<dd>id</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_payer">$payer</a>
    <span>
                &nbsp;: object            </span>
</dt>
<dd>payer</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_payments">$payments</a>
    <span>
                &nbsp;: array&lt;string|int, mixed&gt;            </span>
</dt>
<dd>payments</dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -protected">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_status">$status</a>
    <span>
                &nbsp;: string            </span>
</dt>
<dd>status</dd>

                <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method___construct">__construct()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd>Entity constructor.</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method___get">__get()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method___isset">__isset()</a>
    <span>
                        &nbsp;: bool    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method___set">__set()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_all">all()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_APCIteratorAll">APCIteratorAll()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_cancel">cancel()</a>
    <span>
                        &nbsp;: bool|mixed    </span>
</dt>
<dd>cancel</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_capture">capture()</a>
    <span>
                        &nbsp;: bool|mixed    </span>
</dt>
<dd>capture</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_custom_action">custom_action()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_delete">delete()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_destroy">destroy()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_Error">Error()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_find_by_id">find_by_id()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_get">get()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_getAttributes">getAttributes()</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_getCustomHeader">getCustomHeader()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_getCustomHeaders">getCustomHeaders()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_not_found">not_found()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_process_error_body">process_error_body()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_read">read()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_refund">refund()</a>
    <span>
                        &nbsp;: bool    </span>
</dt>
<dd>refund</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_refundDisbursement">refundDisbursement()</a>
    <span>
                        &nbsp;: bool    </span>
</dt>
<dd>refundDisbursement</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_save">save()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_search">search()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_setCustomHeader">setCustomHeader()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_setCustomHeadersFromArray">setCustomHeadersFromArray()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_setManager">setManager()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_toArray">toArray()</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_unSetManager">unSetManager()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-Entity.html#method_update">update()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__camelize">_camelize()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__fillFromArray">_fillFromArray()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd>Fill entity from data with nested object creation</dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__getDynamicAttributeDenied">_getDynamicAttributeDenied()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__getPropertyType">_getPropertyType()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__propertyExists">_propertyExists()</a>
    <span>
                        &nbsp;: bool    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__propertyTypeAllowed">_propertyTypeAllowed()</a>
    <span>
                        &nbsp;: bool    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method__setValue">_setValue()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -protected">
    <a href="classes/MercadoPago-Entity.html#method_tryFormat">tryFormat()</a>
    <span>
                        &nbsp;: array&lt;string|int, mixed&gt;|bool|float|int|string    </span>
</dt>
<dd></dd>

        </dl>



        

        
    <section class="phpdocumentor-properties">
        <h3 class="phpdocumentor-elements__header" id="properties">
            Properties
            <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#properties" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
            -static                                            "
>
    <h4 class="phpdocumentor-element__name" id="property__custom_headers">
        $_custom_headers
        <a href="classes/MercadoPago-Entity.html#property__custom_headers" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">16</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
    <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_custom_headers</span>
     = <span class="phpdocumentor-signature__default-value">array()</span></code>

        <section class="phpdocumentor-description"></section>

    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property__empty">
        $_empty
        <a href="classes/MercadoPago-Entity.html#property__empty" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">27</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_empty</span>
     = <span class="phpdocumentor-signature__default-value">false</span></code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(serialize = false)</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property__last">
        $_last
        <a href="classes/MercadoPago-Entity.html#property__last" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">21</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_last</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>(serialize = false)</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
            -static                                            "
>
    <h4 class="phpdocumentor-element__name" id="property__manager">
        $_manager
        <a href="classes/MercadoPago-Entity.html#property__manager" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">17</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
    <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_manager</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property__pagination_params">
        $_pagination_params
        <a href="classes/MercadoPago-Entity.html#property__pagination_params" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">23</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_pagination_params</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_application_id">
        $application_id
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_application_id" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">34</span>
</aside>

        <p class="phpdocumentor-summary">application_id</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">int</span>
    <span class="phpdocumentor-signature__name">$application_id</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_binary_mode">
        $binary_mode
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_binary_mode" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">76</span>
</aside>

        <p class="phpdocumentor-summary">binary_mode</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">bool</span>
    <span class="phpdocumentor-signature__name">$binary_mode</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_capture">
        $capture
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_capture" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">90</span>
</aside>

        <p class="phpdocumentor-summary">capture</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">bool</span>
    <span class="phpdocumentor-signature__name">$capture</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_description">
        $description
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_description" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">69</span>
</aside>

        <p class="phpdocumentor-summary">description</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">string</span>
    <span class="phpdocumentor-signature__name">$description</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_disbursements">
        $disbursements
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_disbursements" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">48</span>
</aside>

        <p class="phpdocumentor-summary">disbursements</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">array&lt;string|int, mixed&gt;</span>
    <span class="phpdocumentor-signature__name">$disbursements</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_error">
        $error
        <a href="classes/MercadoPago-Entity.html#property_error" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">22</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$error</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_external_reference">
        $external_reference
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_external_reference" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">62</span>
</aside>

        <p class="phpdocumentor-summary">external_reference</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">string</span>
    <span class="phpdocumentor-signature__name">$external_reference</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_id">
        $id
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_id" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">27</span>
</aside>

        <p class="phpdocumentor-summary">id</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">int</span>
    <span class="phpdocumentor-signature__name">$id</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_payer">
        $payer
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_payer" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">55</span>
</aside>

        <p class="phpdocumentor-summary">payer</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">object</span>
    <span class="phpdocumentor-signature__name">$payer</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_payments">
        $payments
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_payments" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">41</span>
</aside>

        <p class="phpdocumentor-summary">payments</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">array&lt;string|int, mixed&gt;</span>
    <span class="phpdocumentor-signature__name">$payments</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_status">
        $status
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#property_status" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">83</span>
</aside>

        <p class="phpdocumentor-summary">status</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
        <span class="phpdocumentor-signature__type">string</span>
    <span class="phpdocumentor-signature__name">$status</span>
    </code>

        <section class="phpdocumentor-description"></section>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">Attribute</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>()</p>
</section>

                                    </dd>
                        </dl>

</article>
            </section>

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#methods" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___construct">
        __construct()
        <a href="classes/MercadoPago-Entity.html#method___construct" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">35</span>
</aside>

        <p class="phpdocumentor-summary">Entity constructor.</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">__construct</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;&nbsp;</span><span class="phpdocumentor-signature__argument__name">$params</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$params</span>
                : <span class="phpdocumentor-signature__argument__return-type">array&lt;string|int, mixed&gt;</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___get">
        __get()
        <a href="classes/MercadoPago-Entity.html#method___get" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">287</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">__get</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$name</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$name</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___isset">
        __isset()
        <a href="classes/MercadoPago-Entity.html#method___isset" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">299</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">__isset</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$name</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$name</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method___set">
        __set()
        <a href="classes/MercadoPago-Entity.html#method___set" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">310</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">__set</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$name</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$value</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$name</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$value</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_all">
        all()
        <a href="classes/MercadoPago-Entity.html#method_all" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">134</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">all</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_APCIteratorAll">
        APCIteratorAll()
        <a href="classes/MercadoPago-Entity.html#method_APCIteratorAll" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">196</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">APCIteratorAll</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">codeCoverageIgnore</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_cancel">
        cancel()
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_cancel" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">98</span>
</aside>

        <p class="phpdocumentor-summary">cancel</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">cancel</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool|mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_capture">
        capture()
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_capture" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">110</span>
</aside>

        <p class="phpdocumentor-summary">capture</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">capture</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool|mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool|mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_custom_action">
        custom_action()
        <a href="classes/MercadoPago-Entity.html#method_custom_action" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">237</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">custom_action</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$method</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$action</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$method</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$action</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_delete">
        delete()
        <a href="classes/MercadoPago-Entity.html#method_delete" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">501</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">delete</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_destroy">
        destroy()
        <a href="classes/MercadoPago-Entity.html#method_destroy" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">229</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">destroy</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">codeCoverageIgnore</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_Error">
        Error()
        <a href="classes/MercadoPago-Entity.html#method_Error" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">46</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">Error</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_find_by_id">
        find_by_id()
        <a href="classes/MercadoPago-Entity.html#method_find_by_id" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">73</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">find_by_id</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$id</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$id</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_get">
        get()
        <a href="classes/MercadoPago-Entity.html#method_get" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">66</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">get</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$id</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$id</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_getAttributes">
        getAttributes()
        <a href="classes/MercadoPago-Entity.html#method_getAttributes" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">320</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">getAttributes</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

        <section class="phpdocumentor-description"></section>

    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_getCustomHeader">
        getCustomHeader()
        <a href="classes/MercadoPago-Entity.html#method_getCustomHeader" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">81</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">getCustomHeader</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$key</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$key</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_getCustomHeaders">
        getCustomHeaders()
        <a href="classes/MercadoPago-Entity.html#method_getCustomHeaders" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">90</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">getCustomHeaders</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_not_found">
        not_found()
        <a href="classes/MercadoPago-Entity.html#method_not_found" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">98</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">not_found</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_process_error_body">
        process_error_body()
        <a href="classes/MercadoPago-Entity.html#method_process_error_body" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">272</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">process_error_body</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$message</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$message</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_read">
        read()
        <a href="classes/MercadoPago-Entity.html#method_read" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">106</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">read</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$params</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$params</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_refund">
        refund()
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_refund" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">124</span>
</aside>

        <p class="phpdocumentor-summary">refund</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">refund</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$amount</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$amount</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_refundDisbursement">
        refundDisbursement()
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#method_refundDisbursement" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php"><a href="files/src-mercadopago-entities-advancedpayments-advancedpayment.html"><abbr title="src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php">AdvancedPayment.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">148</span>
</aside>

        <p class="phpdocumentor-summary">refundDisbursement</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">refundDisbursement</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$disbursement_id</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">int&nbsp;</span><span class="phpdocumentor-signature__argument__name">$amount</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$disbursement_id</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$amount</span>
                : <span class="phpdocumentor-signature__argument__return-type">int</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_save">
        save()
        <a href="classes/MercadoPago-Entity.html#method_save" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">251</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">save</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_search">
        search()
        <a href="classes/MercadoPago-Entity.html#method_search" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">163</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">search</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$filters</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$filters</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_setCustomHeader">
        setCustomHeader()
        <a href="classes/MercadoPago-Entity.html#method_setCustomHeader" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">77</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">setCustomHeader</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$key</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$value</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$key</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$value</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_setCustomHeadersFromArray">
        setCustomHeadersFromArray()
        <a href="classes/MercadoPago-Entity.html#method_setCustomHeadersFromArray" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">85</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">setCustomHeadersFromArray</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$array</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$array</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_setManager">
        setManager()
        <a href="classes/MercadoPago-Entity.html#method_setManager" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">53</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">setManager</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Manager.html"><abbr title="\MercadoPago\Manager">Manager</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$manager</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$manager</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Manager.html"><abbr title="\MercadoPago\Manager">Manager</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_toArray">
        toArray()
        <a href="classes/MercadoPago-Entity.html#method_toArray" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">328</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">toArray</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">null&nbsp;</span><span class="phpdocumentor-signature__argument__name">$attributes</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">null</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$attributes</span>
                : <span class="phpdocumentor-signature__argument__return-type">null</span>
                 = <span class="phpdocumentor-signature__argument__default-value">null</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_unSetManager">
        unSetManager()
        <a href="classes/MercadoPago-Entity.html#method_unSetManager" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">59</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">unSetManager</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_update">
        update()
        <a href="classes/MercadoPago-Entity.html#method_update" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">205</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">update</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$options</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">[]</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$options</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">[]</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__camelize">
        _camelize()
        <a href="classes/MercadoPago-Entity.html#method__camelize" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">495</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_camelize</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$input</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">string&nbsp;</span><span class="phpdocumentor-signature__argument__name">$separator</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039;_&#039;</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$input</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$separator</span>
                : <span class="phpdocumentor-signature__argument__return-type">string</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039;_&#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__fillFromArray">
        _fillFromArray()
        <a href="classes/MercadoPago-Entity.html#method__fillFromArray" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">467</span>
</aside>

        <p class="phpdocumentor-summary">Fill entity from data with nested object creation</p>

    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_fillFromArray</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$entity</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$data</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$entity</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$data</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__getDynamicAttributeDenied">
        _getDynamicAttributeDenied()
        <a href="classes/MercadoPago-Entity.html#method__getDynamicAttributeDenied" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">415</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_getDynamicAttributeDenied</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__getPropertyType">
        _getPropertyType()
        <a href="classes/MercadoPago-Entity.html#method__getPropertyType" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">408</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_getPropertyType</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$property</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$property</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__propertyExists">
        _propertyExists()
        <a href="classes/MercadoPago-Entity.html#method__propertyExists" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">382</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_propertyExists</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$property</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$property</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__propertyTypeAllowed">
        _propertyTypeAllowed()
        <a href="classes/MercadoPago-Entity.html#method__propertyTypeAllowed" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">392</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_propertyTypeAllowed</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$property</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$type</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">bool</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$property</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$type</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">bool</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method__setValue">
        _setValue()
        <a href="classes/MercadoPago-Entity.html#method__setValue" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">359</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">_setValue</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$property</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$value</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$validate</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">true</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$property</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$value</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$validate</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">true</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -protected
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_tryFormat">
        tryFormat()
        <a href="classes/MercadoPago-Entity.html#method_tryFormat" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Entity.php"><a href="files/src-mercadopago-entity.html"><abbr title="src/MercadoPago/Entity.php">Entity.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">427</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">protected</span>
                <span class="phpdocumentor-signature__name">tryFormat</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$value</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$type</span></span><span class="phpdocumentor-signature__argument"><span>, </span><span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$property</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|bool|float|int|string</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$value</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$type</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$property</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href=""><abbr title=""></abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">throws</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                <span class="phpdocumentor-tag-link"><abbr title="\Exception">Exception</abbr></span>
                                                            
                                                 <section class="phpdocumentor-description"></section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">array&lt;string|int, mixed&gt;|bool|float|int|string</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
            </section>

    </article>
                <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
            </div>
        </div>
        <a href="classes/MercadoPago-AdvancedPayments-AdvancedPayment.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
</body>
</html>
