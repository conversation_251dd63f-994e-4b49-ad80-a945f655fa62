<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Documentation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/js/all.min.js" integrity="sha256-0vuk8LXoyrmCjp1f0O300qo1M75ZQyhH9X3J6d+scmk=" crossorigin="anonymous"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -four phpdocumentor-sidebar">
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                <h3 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/default.html"><abbr title="\">Global</abbr></a></h3>
                                        <h4 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/mercadopago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h4>
                <ul class="phpdocumentor-list">
                                            <li><a href="namespaces/mercadopago-annotation.html"><abbr title="\MercadoPago\Annotation">Annotation</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-advancedpayments.html"><abbr title="\MercadoPago\AdvancedPayments">AdvancedPayments</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                                    </ul>
                        </section>

        <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                    <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/Default.html"><abbr title="\Default">Default</abbr></a></h3>
                                <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/MercadoPago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h3>
                        <ul class="phpdocumentor-list">
                                    <li><a href="packages/MercadoPago-Config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                    <li><a href="packages/MercadoPago-Http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                            </ul>
                        </section>
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -eight phpdocumentor-content">
                    <ul class="phpdocumentor-breadcrumbs">
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/mercadopago.html">MercadoPago</a></li>
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    SearchResultsArray

        <span class="phpdocumentor-element__extends">
        extends <abbr title="\ArrayObject">ArrayObject</abbr>
    </span>
    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/Default.html">Default</a></li>
                            </ul>
        </div>
    
    
    </h2>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">6</span>
</aside>

        








<h3 id="toc">
    Table of Contents
    <a href="#toc" class="headerlink"><i class="fas fa-link"></i></a>
</h3>

<dl class="phpdocumentor-table-of-contents">
                <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property__class">$_class</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property__filters">$_filters</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property_errors">$errors</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property_limit">$limit</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property_offset">$offset</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -property -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#property_total">$total</a>
    <span>
                &nbsp;: mixed            </span>
</dt>
<dd></dd>

                <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#method_next">next()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#method_process_error_body">process_error_body()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#method_setEntityTypes">setEntityTypes()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/MercadoPago-SearchResultsArray.html#method_setPaginateParams">setPaginateParams()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

        </dl>



        

        
    <section class="phpdocumentor-properties">
        <h3 class="phpdocumentor-elements__header" id="properties">
            Properties
            <a href="classes/MercadoPago-SearchResultsArray.html#properties" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property__class">
        $_class
        <a href="classes/MercadoPago-SearchResultsArray.html#property__class" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">13</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_class</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property__filters">
        $_filters
        <a href="classes/MercadoPago-SearchResultsArray.html#property__filters" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">8</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$_filters</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_errors">
        $errors
        <a href="classes/MercadoPago-SearchResultsArray.html#property_errors" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">12</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$errors</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_limit">
        $limit
        <a href="classes/MercadoPago-SearchResultsArray.html#property_limit" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">9</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$limit</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_offset">
        $offset
        <a href="classes/MercadoPago-SearchResultsArray.html#property_offset" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">11</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$offset</span>
    </code>

    
    

</article>
                    <article
        class="
            phpdocumentor-element
            -property
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="property_total">
        $total
        <a href="classes/MercadoPago-SearchResultsArray.html#property_total" class="headerlink"><i class="fas fa-link"></i></a>
        <span class="phpdocumentor-element__modifiers">
                                </span>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">10</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
        <span class="phpdocumentor-signature__type">mixed</span>
    <span class="phpdocumentor-signature__name">$total</span>
    </code>

    
    

</article>
            </section>

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/MercadoPago-SearchResultsArray.html#methods" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_next">
        next()
        <a href="classes/MercadoPago-SearchResultsArray.html#method_next" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">26</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">next</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_process_error_body">
        process_error_body()
        <a href="classes/MercadoPago-SearchResultsArray.html#method_process_error_body" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">48</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">process_error_body</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$message</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$message</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_setEntityTypes">
        setEntityTypes()
        <a href="classes/MercadoPago-SearchResultsArray.html#method_setEntityTypes" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">16</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">setEntityTypes</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$class</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$class</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_setPaginateParams">
        setPaginateParams()
        <a href="classes/MercadoPago-SearchResultsArray.html#method_setPaginateParams" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/MercadoPago/Generic/SearchResultsArray.php"><a href="files/src-mercadopago-generic-searchresultsarray.html"><abbr title="src/MercadoPago/Generic/SearchResultsArray.php">SearchResultsArray.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">20</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">setPaginateParams</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$params</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$params</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
            </section>

    </article>
                <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
            </div>
        </div>
        <a href="classes/MercadoPago-SearchResultsArray.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
</body>
</html>
