<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta charset="utf-8"/>
    <title>    SDK Mercado Pago &raquo; \MercadoPago\Entity
</title>
    <meta name="author" content=""/>
    <meta name="description" content=""/>

            <link href="../css/template.css" rel="stylesheet" media="all"/>
    
            <!--[if lt IE 9]>
        <script src="https://html5shim.googlecode.com/svn/trunk/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script>
        <script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
        <script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script>
        <script src="../js/bootstrap.js" type="text/javascript"></script>
        <script src="../js/template.js" type="text/javascript"></script>
        <script src="../js/prettify/prettify.min.js" type="text/javascript"></script>
    
            <link rel="shortcut icon" href="../img/favicon.ico"/>
        <link rel="apple-touch-icon" href="../img/apple-touch-icon.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png"/>
    </head>
<body>

        <div class="navbar navbar-fixed-top">
        <div class="navbar-inner">
            <div class="container">
                <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span>
                    <span class="icon-bar"></span> </a>
                <a class="brand" href="../index.html">SDK Mercado Pago</a>

                <div class="nav-collapse">
                    <ul class="nav">
                        <li class="dropdown">
                            <a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                API Documentation <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                                                    <li><a>Namespaces</a></li>
                                                                        <li><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>
                                                                                                    
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                            </ul>
                        </li>
                        <li class="dropdown" id="charts-menu">
                            <a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                Charts <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../graph_class.html">
                                        <i class="icon-list-alt"></i>&#160;Class hierarchy diagram
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown" id="reports-menu">
                            <a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                Reports <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../errors.html">
                                        <i class="icon-list-alt"></i>&#160;Errors
                                    </a>
                                </li>
                                <li>
                                    <a href="../markers.html">
                                        <i class="icon-list-alt"></i>&#160;Markers
                                    </a>
                                </li>
                                <li>
                                    <a href="../deprecated.html">
                                        <i class="icon-list-alt"></i>&#160;Deprecated
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="go_to_top">
            <a href="#___" style="color: inherit">Back to top&#160;&#160;<i class="icon-upload icon-white"></i></a>
        </div>
    </div>
    
    <div id="___" class="container">
        <noscript>
            <div class="alert alert-warning">
                Javascript is disabled; several features are only available if Javascript is enabled.
            </div>
        </noscript>

        
            <style>
        .deprecated h2 {
            text-decoration: line-through;
        }
    </style>
    <div class="row">
        <div class="span4">
                    <div class="btn-group view pull-right" data-toggle="buttons-radio">
        <button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button>
        <button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
    </div>
    <div class="btn-group visibility" data-toggle="buttons-checkbox">
        <button class="btn public active" title="Show public elements">Public</button>
        <button class="btn protected" title="Show protected elements">Protected</button>
        <button class="btn private" title="Show private elements">Private</button>
        <button class="btn inherited active" title="Show inherited elements">Inherited</button>
    </div>

        <ul class="side-nav nav nav-list">
        <li class="nav-header">
            <i class="icon-custom icon-method"></i> Methods
            <ul>
                                                                                                    <li class="method public">
        <a href="#method___construct" title="__construct :: Entity constructor.">
            <span class="description">Entity constructor.</span><pre>__construct</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method___get" title="__get :: ">
            <span class="description"></span><pre>__get</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method___isset" title="__isset :: ">
            <span class="description"></span><pre>__isset</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method___set" title="__set :: ">
            <span class="description"></span><pre>__set</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_all" title="all :: ">
            <span class="description"></span><pre>all</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_APCIteratorAll" title="APCIteratorAll :: ">
            <span class="description"></span><pre>APCIteratorAll</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_custom_action" title="custom_action :: ">
            <span class="description"></span><pre>custom_action</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_delete" title="delete :: ">
            <span class="description"></span><pre>delete</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_destroy" title="destroy :: ">
            <span class="description"></span><pre>destroy</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_Error" title="Error :: ">
            <span class="description"></span><pre>Error</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_find_by_id" title="find_by_id :: ">
            <span class="description"></span><pre>find_by_id</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_get" title="get :: ">
            <span class="description"></span><pre>get</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getAttributes" title="getAttributes :: ">
            <span class="description"></span><pre>getAttributes</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getCustomHeader" title="getCustomHeader :: ">
            <span class="description"></span><pre>getCustomHeader</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getCustomHeaders" title="getCustomHeaders :: ">
            <span class="description"></span><pre>getCustomHeaders</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_not_found" title="not_found :: ">
            <span class="description"></span><pre>not_found</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_process_error_body" title="process_error_body :: ">
            <span class="description"></span><pre>process_error_body</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_read" title="read :: ">
            <span class="description"></span><pre>read</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_save" title="save :: ">
            <span class="description"></span><pre>save</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_search" title="search :: ">
            <span class="description"></span><pre>search</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setCustomHeader" title="setCustomHeader :: ">
            <span class="description"></span><pre>setCustomHeader</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setCustomHeadersFromArray" title="setCustomHeadersFromArray :: ">
            <span class="description"></span><pre>setCustomHeadersFromArray</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setManager" title="setManager :: ">
            <span class="description"></span><pre>setManager</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_toArray" title="toArray :: ">
            <span class="description"></span><pre>toArray</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_unSetManager" title="unSetManager :: ">
            <span class="description"></span><pre>unSetManager</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_update" title="update :: ">
            <span class="description"></span><pre>update</pre>
        </a>
    </li>

                                                </ul>
        </li>
        <li class="nav-header protected">» Protected
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </ul>
        </li>
        <li class="nav-header private">» Private
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    </ul>
        </li>
        <li class="nav-header">
            <i class="icon-custom icon-constant"></i> Constants
            <ul>
                            </ul>
        </li>
    </ul>


        </div>

        <div class="span8">
            <div class="element class">
                <h1>Entity</h1>
                <small style="display: block; text-align: right">
                                                        </small>
                <p class="short_description">Class Entity</p>
                <div class="details">
                    <div class="long_description">
                        
                    </div>
                    <table class="table table-bordered">
                                                                                    <tr>
                                    <th>
                                        package
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>MercadoPago</p>
                                    </td>
                                </tr>
                                                                                                                                                                                                                                                                                                                                            </table>

                    <h3><i class="icon-custom icon-method"></i> Methods</h3>
                                                                <a id="method___construct"></a>
                        <div class="element clickable method public  method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
                            <h2>Entity constructor.</h2>
                            <pre>__construct(array $params = array()) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                                                            <tr>
                                                <th>Throws</th>
                                                <td>
                                                    <dl>
                                                                                                            <dt>\Exception</dt>
                                                        <dd></dd>
                                                                                                                                                            </dl>
                                                </td>
                                            </tr>
                                                                            </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$params</h4>
                                                <code>array</code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method___get"></a>
                        <div class="element clickable method public  method___get" data-toggle="collapse" data-target=".method___get .collapse">
                            <h2>__get</h2>
                            <pre>__get( $name) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$name</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method___isset"></a>
                        <div class="element clickable method public  method___isset" data-toggle="collapse" data-target=".method___isset .collapse">
                            <h2>__isset</h2>
                            <pre>__isset( $name) : boolean</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$name</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>boolean</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method___set"></a>
                        <div class="element clickable method public  method___set" data-toggle="collapse" data-target=".method___set .collapse">
                            <h2>__set</h2>
                            <pre>__set( $name,  $value) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                                                            <tr>
                                                <th>Throws</th>
                                                <td>
                                                    <dl>
                                                                                                            <dt>\Exception</dt>
                                                        <dd></dd>
                                                                                                                                                            </dl>
                                                </td>
                                            </tr>
                                                                            </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$name</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_all"></a>
                        <div class="element clickable method public  method_all" data-toggle="collapse" data-target=".method_all .collapse">
                            <h2>all</h2>
                            <pre>all( $options = array()) : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_APCIteratorAll"></a>
                        <div class="element clickable method public  method_APCIteratorAll" data-toggle="collapse" data-target=".method_APCIteratorAll .collapse">
                            <h2>APCIteratorAll</h2>
                            <pre>APCIteratorAll() : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_custom_action"></a>
                        <div class="element clickable method public  method_custom_action" data-toggle="collapse" data-target=".method_custom_action .collapse">
                            <h2>custom_action</h2>
                            <pre>custom_action( $method,  $action) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$method</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$action</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_delete"></a>
                        <div class="element clickable method public  method_delete" data-toggle="collapse" data-target=".method_delete .collapse">
                            <h2>delete</h2>
                            <pre>delete( $options = array()) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_destroy"></a>
                        <div class="element clickable method public  method_destroy" data-toggle="collapse" data-target=".method_destroy .collapse">
                            <h2>destroy</h2>
                            <pre>destroy() : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_Error"></a>
                        <div class="element clickable method public  method_Error" data-toggle="collapse" data-target=".method_Error .collapse">
                            <h2>Error</h2>
                            <pre>Error() </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_find_by_id"></a>
                        <div class="element clickable method public  method_find_by_id" data-toggle="collapse" data-target=".method_find_by_id .collapse">
                            <h2>find_by_id</h2>
                            <pre>find_by_id( $id) : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_get"></a>
                        <div class="element clickable method public  method_get" data-toggle="collapse" data-target=".method_get .collapse">
                            <h2>get</h2>
                            <pre>get( $id) : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getAttributes"></a>
                        <div class="element clickable method public  method_getAttributes" data-toggle="collapse" data-target=".method_getAttributes .collapse">
                            <h2>getAttributes</h2>
                            <pre>getAttributes() : array</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>array</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getCustomHeader"></a>
                        <div class="element clickable method public  method_getCustomHeader" data-toggle="collapse" data-target=".method_getCustomHeader .collapse">
                            <h2>getCustomHeader</h2>
                            <pre>getCustomHeader( $key) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$key</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getCustomHeaders"></a>
                        <div class="element clickable method public  method_getCustomHeaders" data-toggle="collapse" data-target=".method_getCustomHeaders .collapse">
                            <h2>getCustomHeaders</h2>
                            <pre>getCustomHeaders() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_not_found"></a>
                        <div class="element clickable method public  method_not_found" data-toggle="collapse" data-target=".method_not_found .collapse">
                            <h2>not_found</h2>
                            <pre>not_found() : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_process_error_body"></a>
                        <div class="element clickable method public  method_process_error_body" data-toggle="collapse" data-target=".method_process_error_body .collapse">
                            <h2>process_error_body</h2>
                            <pre>process_error_body( $message) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$message</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_read"></a>
                        <div class="element clickable method public  method_read" data-toggle="collapse" data-target=".method_read .collapse">
                            <h2>read</h2>
                            <pre>read( $params = array(),  $options = array()) : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$params</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_save"></a>
                        <div class="element clickable method public  method_save" data-toggle="collapse" data-target=".method_save .collapse">
                            <h2>save</h2>
                            <pre>save( $options = array()) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_search"></a>
                        <div class="element clickable method public  method_search" data-toggle="collapse" data-target=".method_search .collapse">
                            <h2>search</h2>
                            <pre>search( $filters = array(),  $options = array()) : mixed</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$filters</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setCustomHeader"></a>
                        <div class="element clickable method public  method_setCustomHeader" data-toggle="collapse" data-target=".method_setCustomHeader .collapse">
                            <h2>setCustomHeader</h2>
                            <pre>setCustomHeader( $key,  $value) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$key</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setCustomHeadersFromArray"></a>
                        <div class="element clickable method public  method_setCustomHeadersFromArray" data-toggle="collapse" data-target=".method_setCustomHeadersFromArray .collapse">
                            <h2>setCustomHeadersFromArray</h2>
                            <pre>setCustomHeadersFromArray( $array) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$array</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setManager"></a>
                        <div class="element clickable method public  method_setManager" data-toggle="collapse" data-target=".method_setManager .collapse">
                            <h2>setManager</h2>
                            <pre>setManager(\MercadoPago\Manager $manager) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$manager</h4>
                                                <code><a href="../classes/MercadoPago.Manager.html">\MercadoPago\Manager</a></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_toArray"></a>
                        <div class="element clickable method public  method_toArray" data-toggle="collapse" data-target=".method_toArray .collapse">
                            <h2>toArray</h2>
                            <pre>toArray(null $attributes = null) : array</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$attributes</h4>
                                                <code>null</code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>array</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_unSetManager"></a>
                        <div class="element clickable method public  method_unSetManager" data-toggle="collapse" data-target=".method_unSetManager .collapse">
                            <h2>unSetManager</h2>
                            <pre>unSetManager() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_update"></a>
                        <div class="element clickable method public  method_update" data-toggle="collapse" data-target=".method_update .collapse">
                            <h2>update</h2>
                            <pre>update( $options = array()) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                    
                    
                                                        </div>
            </div>
            <a id="\MercadoPago\Entity"></a>
            <ul class="breadcrumb">
                <li><a href="../index.html"><i class="icon-custom icon-class"></i></a></li>
                    
    
    <li><span class="divider">\</span><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>

                <li class="active"><span class="divider">\</span><a href="../classes/MercadoPago.Entity.html">Entity</a></li>
            </ul>
        </div>
    </div>

    </div>

        <footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by
            <a href="http://glyphicons.com/">Glyphicons</a>.<br/>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor </a> and<br/>
            generated on Thu, 25 Jun 2020 12:36:19 +0000.<br/>
    </footer>
    </body>
</html>
