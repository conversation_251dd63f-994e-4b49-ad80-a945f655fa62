<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta charset="utf-8"/>
    <title>    SDK Mercado Pago &raquo; \MercadoPago\Http\CurlRequest
</title>
    <meta name="author" content=""/>
    <meta name="description" content=""/>

            <link href="../css/template.css" rel="stylesheet" media="all"/>
    
            <!--[if lt IE 9]>
        <script src="https://html5shim.googlecode.com/svn/trunk/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script>
        <script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
        <script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script>
        <script src="../js/bootstrap.js" type="text/javascript"></script>
        <script src="../js/template.js" type="text/javascript"></script>
        <script src="../js/prettify/prettify.min.js" type="text/javascript"></script>
    
            <link rel="shortcut icon" href="../img/favicon.ico"/>
        <link rel="apple-touch-icon" href="../img/apple-touch-icon.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png"/>
    </head>
<body>

        <div class="navbar navbar-fixed-top">
        <div class="navbar-inner">
            <div class="container">
                <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span>
                    <span class="icon-bar"></span> </a>
                <a class="brand" href="../index.html">SDK Mercado Pago</a>

                <div class="nav-collapse">
                    <ul class="nav">
                        <li class="dropdown">
                            <a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                API Documentation <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                                                    <li><a>Namespaces</a></li>
                                                                        <li><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>
                                                                                                    
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                            </ul>
                        </li>
                        <li class="dropdown" id="charts-menu">
                            <a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                Charts <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../graph_class.html">
                                        <i class="icon-list-alt"></i>&#160;Class hierarchy diagram
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown" id="reports-menu">
                            <a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                Reports <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../errors.html">
                                        <i class="icon-list-alt"></i>&#160;Errors
                                    </a>
                                </li>
                                <li>
                                    <a href="../markers.html">
                                        <i class="icon-list-alt"></i>&#160;Markers
                                    </a>
                                </li>
                                <li>
                                    <a href="../deprecated.html">
                                        <i class="icon-list-alt"></i>&#160;Deprecated
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="go_to_top">
            <a href="#___" style="color: inherit">Back to top&#160;&#160;<i class="icon-upload icon-white"></i></a>
        </div>
    </div>
    
    <div id="___" class="container">
        <noscript>
            <div class="alert alert-warning">
                Javascript is disabled; several features are only available if Javascript is enabled.
            </div>
        </noscript>

        
            <style>
        .deprecated h2 {
            text-decoration: line-through;
        }
    </style>
    <div class="row">
        <div class="span4">
                    <div class="btn-group view pull-right" data-toggle="buttons-radio">
        <button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button>
        <button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
    </div>
    <div class="btn-group visibility" data-toggle="buttons-checkbox">
        <button class="btn public active" title="Show public elements">Public</button>
        <button class="btn protected" title="Show protected elements">Protected</button>
        <button class="btn private" title="Show private elements">Private</button>
        <button class="btn inherited active" title="Show inherited elements">Inherited</button>
    </div>

        <ul class="side-nav nav nav-list">
        <li class="nav-header">
            <i class="icon-custom icon-method"></i> Methods
            <ul>
                                                                                                    <li class="method public">
        <a href="#method___construct" title="__construct :: ">
            <span class="description"></span><pre>__construct</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_close" title="close :: ">
            <span class="description"></span><pre>close</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_error" title="error :: ">
            <span class="description"></span><pre>error</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_execute" title="execute :: ">
            <span class="description"></span><pre>execute</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getInfo" title="getInfo :: ">
            <span class="description"></span><pre>getInfo</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setOption" title="setOption :: ">
            <span class="description"></span><pre>setOption</pre>
        </a>
    </li>

                                                </ul>
        </li>
        <li class="nav-header protected">» Protected
            <ul>
                                                                                                                                                                                                                                                                    </ul>
        </li>
        <li class="nav-header private">» Private
            <ul>
                                                                                                                                                                                                                                                                    </ul>
        </li>
        <li class="nav-header">
            <i class="icon-custom icon-constant"></i> Constants
            <ul>
                            </ul>
        </li>
    </ul>


        </div>

        <div class="span8">
            <div class="element class">
                <h1>CurlRequest</h1>
                <small style="display: block; text-align: right">
                                                                Implements <a href="../classes/MercadoPago.Http.HttpRequest.html">\MercadoPago\Http\HttpRequest</a>
                                    </small>
                <p class="short_description">CurlRequest Class Doc Comment</p>
                <div class="details">
                    <div class="long_description">
                        
                    </div>
                    <table class="table table-bordered">
                                                                                    <tr>
                                    <th>
                                        package
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>MercadoPago\Http</p>
                                    </td>
                                </tr>
                                                                                                                                                                                </table>

                    <h3><i class="icon-custom icon-method"></i> Methods</h3>
                                                                <a id="method___construct"></a>
                        <div class="element clickable method public  method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
                            <h2>__construct</h2>
                            <pre>__construct(null $uri = null) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        <p>CurlRequest constructor.</p>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$uri</h4>
                                                <code>null</code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_close"></a>
                        <div class="element clickable method public  method_close" data-toggle="collapse" data-target=".method_close .collapse">
                            <h2>close</h2>
                            <pre>close() : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_error"></a>
                        <div class="element clickable method public  method_error" data-toggle="collapse" data-target=".method_error .collapse">
                            <h2>error</h2>
                            <pre>error() : string</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>string</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_execute"></a>
                        <div class="element clickable method public  method_execute" data-toggle="collapse" data-target=".method_execute .collapse">
                            <h2>execute</h2>
                            <pre>execute() : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getInfo"></a>
                        <div class="element clickable method public  method_getInfo" data-toggle="collapse" data-target=".method_getInfo .collapse">
                            <h2>getInfo</h2>
                            <pre>getInfo( $name) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$name</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setOption"></a>
                        <div class="element clickable method public  method_setOption" data-toggle="collapse" data-target=".method_setOption .collapse">
                            <h2>setOption</h2>
                            <pre>setOption( $name,  $value) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    codeCoverageIgnore
                                                </th>
                                                <td>
                                                                                                                                                                        
                                                                                                                                                                        
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$name</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                    
                    
                                                        </div>
            </div>
            <a id="\MercadoPago\Http\CurlRequest"></a>
            <ul class="breadcrumb">
                <li><a href="../index.html"><i class="icon-custom icon-class"></i></a></li>
                    
    
    <li><span class="divider">\</span><a href="../namespaces/MercadoPago.Http.html">Http</a></li>

                <li class="active"><span class="divider">\</span><a href="../classes/MercadoPago.Http.CurlRequest.html">CurlRequest</a></li>
            </ul>
        </div>
    </div>

    </div>

        <footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by
            <a href="http://glyphicons.com/">Glyphicons</a>.<br/>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor </a> and<br/>
            generated on Thu, 25 Jun 2020 12:36:19 +0000.<br/>
    </footer>
    </body>
</html>
