<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta charset="utf-8"/>
    <title>    SDK Mercado Pago &raquo; \MercadoPago\Manager
</title>
    <meta name="author" content=""/>
    <meta name="description" content=""/>

            <link href="../css/template.css" rel="stylesheet" media="all"/>
    
            <!--[if lt IE 9]>
        <script src="https://html5shim.googlecode.com/svn/trunk/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script>
        <script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
        <script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script>
        <script src="../js/bootstrap.js" type="text/javascript"></script>
        <script src="../js/template.js" type="text/javascript"></script>
        <script src="../js/prettify/prettify.min.js" type="text/javascript"></script>
    
            <link rel="shortcut icon" href="../img/favicon.ico"/>
        <link rel="apple-touch-icon" href="../img/apple-touch-icon.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png"/>
    </head>
<body>

        <div class="navbar navbar-fixed-top">
        <div class="navbar-inner">
            <div class="container">
                <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span>
                    <span class="icon-bar"></span> </a>
                <a class="brand" href="../index.html">SDK Mercado Pago</a>

                <div class="nav-collapse">
                    <ul class="nav">
                        <li class="dropdown">
                            <a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                API Documentation <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                                                    <li><a>Namespaces</a></li>
                                                                        <li><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>
                                                                                                    
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                            </ul>
                        </li>
                        <li class="dropdown" id="charts-menu">
                            <a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                Charts <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../graph_class.html">
                                        <i class="icon-list-alt"></i>&#160;Class hierarchy diagram
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown" id="reports-menu">
                            <a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                Reports <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../errors.html">
                                        <i class="icon-list-alt"></i>&#160;Errors
                                    </a>
                                </li>
                                <li>
                                    <a href="../markers.html">
                                        <i class="icon-list-alt"></i>&#160;Markers
                                    </a>
                                </li>
                                <li>
                                    <a href="../deprecated.html">
                                        <i class="icon-list-alt"></i>&#160;Deprecated
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="go_to_top">
            <a href="#___" style="color: inherit">Back to top&#160;&#160;<i class="icon-upload icon-white"></i></a>
        </div>
    </div>
    
    <div id="___" class="container">
        <noscript>
            <div class="alert alert-warning">
                Javascript is disabled; several features are only available if Javascript is enabled.
            </div>
        </noscript>

        
            <style>
        .deprecated h2 {
            text-decoration: line-through;
        }
    </style>
    <div class="row">
        <div class="span4">
                    <div class="btn-group view pull-right" data-toggle="buttons-radio">
        <button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button>
        <button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
    </div>
    <div class="btn-group visibility" data-toggle="buttons-checkbox">
        <button class="btn public active" title="Show public elements">Public</button>
        <button class="btn protected" title="Show protected elements">Protected</button>
        <button class="btn private" title="Show private elements">Private</button>
        <button class="btn inherited active" title="Show inherited elements">Inherited</button>
    </div>

        <ul class="side-nav nav nav-list">
        <li class="nav-header">
            <i class="icon-custom icon-method"></i> Methods
            <ul>
                                                                                                    <li class="method public">
        <a href="#method___construct" title="__construct :: Manager constructor.">
            <span class="description">Manager constructor.</span><pre>__construct</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_addCustomTrackingParam" title="addCustomTrackingParam :: ">
            <span class="description"></span><pre>addCustomTrackingParam</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_cleanEntityDeltaQueryJsonData" title="cleanEntityDeltaQueryJsonData :: ">
            <span class="description"></span><pre>cleanEntityDeltaQueryJsonData</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_cleanQueryParams" title="cleanQueryParams :: ">
            <span class="description"></span><pre>cleanQueryParams</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_execute" title="execute :: ">
            <span class="description"></span><pre>execute</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getDynamicAttributeDenied" title="getDynamicAttributeDenied :: ">
            <span class="description"></span><pre>getDynamicAttributeDenied</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getExcludedAttributes" title="getExcludedAttributes :: ">
            <span class="description"></span><pre>getExcludedAttributes</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getPropertyType" title="getPropertyType :: ">
            <span class="description"></span><pre>getPropertyType</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_processOptions" title="processOptions :: ">
            <span class="description"></span><pre>processOptions</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setEntityDeltaQueryJsonData" title="setEntityDeltaQueryJsonData :: ">
            <span class="description"></span><pre>setEntityDeltaQueryJsonData</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setEntityMetadata" title="setEntityMetadata :: ">
            <span class="description"></span><pre>setEntityMetadata</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setEntityQueryJsonData" title="setEntityQueryJsonData :: ">
            <span class="description"></span><pre>setEntityQueryJsonData</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setEntityUrl" title="setEntityUrl :: ">
            <span class="description"></span><pre>setEntityUrl</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setQueryParams" title="setQueryParams :: ">
            <span class="description"></span><pre>setQueryParams</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setRawQueryJsonData" title="setRawQueryJsonData :: ">
            <span class="description"></span><pre>setRawQueryJsonData</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_validateAttribute" title="validateAttribute :: ">
            <span class="description"></span><pre>validateAttribute</pre>
        </a>
    </li>

                                                </ul>
        </li>
        <li class="nav-header protected">» Protected
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </ul>
        </li>
        <li class="nav-header private">» Private
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            </ul>
        </li>
        <li class="nav-header">
            <i class="icon-custom icon-constant"></i> Constants
            <ul>
                            </ul>
        </li>
    </ul>


        </div>

        <div class="span8">
            <div class="element class">
                <h1>Manager</h1>
                <small style="display: block; text-align: right">
                                                        </small>
                <p class="short_description">Class Manager</p>
                <div class="details">
                    <div class="long_description">
                        
                    </div>
                    <table class="table table-bordered">
                                                                                    <tr>
                                    <th>
                                        package
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>MercadoPago</p>
                                    </td>
                                </tr>
                                                                                                                                                                                </table>

                    <h3><i class="icon-custom icon-method"></i> Methods</h3>
                                                                <a id="method___construct"></a>
                        <div class="element clickable method public  method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
                            <h2>Manager constructor.</h2>
                            <pre>__construct(\MercadoPago\RestClient $client, \MercadoPago\Config $config) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$client</h4>
                                                <code><a href="../classes/MercadoPago.RestClient.html">\MercadoPago\RestClient</a></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$config</h4>
                                                <code><a href="../classes/MercadoPago.Config.html">\MercadoPago\Config</a></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_addCustomTrackingParam"></a>
                        <div class="element clickable method public  method_addCustomTrackingParam" data-toggle="collapse" data-target=".method_addCustomTrackingParam .collapse">
                            <h2>addCustomTrackingParam</h2>
                            <pre>addCustomTrackingParam( $key,  $value) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$key</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_cleanEntityDeltaQueryJsonData"></a>
                        <div class="element clickable method public  method_cleanEntityDeltaQueryJsonData" data-toggle="collapse" data-target=".method_cleanEntityDeltaQueryJsonData .collapse">
                            <h2>cleanEntityDeltaQueryJsonData</h2>
                            <pre>cleanEntityDeltaQueryJsonData( $entity) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_cleanQueryParams"></a>
                        <div class="element clickable method public  method_cleanQueryParams" data-toggle="collapse" data-target=".method_cleanQueryParams .collapse">
                            <h2>cleanQueryParams</h2>
                            <pre>cleanQueryParams( $entity) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_execute"></a>
                        <div class="element clickable method public  method_execute" data-toggle="collapse" data-target=".method_execute .collapse">
                            <h2>execute</h2>
                            <pre>execute( $entity, string $method = &#039;get&#039;,  $options = array()) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$method</h4>
                                                <code>string</code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getDynamicAttributeDenied"></a>
                        <div class="element clickable method public  method_getDynamicAttributeDenied" data-toggle="collapse" data-target=".method_getDynamicAttributeDenied .collapse">
                            <h2>getDynamicAttributeDenied</h2>
                            <pre>getDynamicAttributeDenied( $entity) : boolean</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>boolean</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getExcludedAttributes"></a>
                        <div class="element clickable method public  method_getExcludedAttributes" data-toggle="collapse" data-target=".method_getExcludedAttributes .collapse">
                            <h2>getExcludedAttributes</h2>
                            <pre>getExcludedAttributes( $entity) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getPropertyType"></a>
                        <div class="element clickable method public  method_getPropertyType" data-toggle="collapse" data-target=".method_getPropertyType .collapse">
                            <h2>getPropertyType</h2>
                            <pre>getPropertyType( $entity,  $property) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$property</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_processOptions"></a>
                        <div class="element clickable method public  method_processOptions" data-toggle="collapse" data-target=".method_processOptions .collapse">
                            <h2>processOptions</h2>
                            <pre>processOptions( $options,  $configuration) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$configuration</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setEntityDeltaQueryJsonData"></a>
                        <div class="element clickable method public  method_setEntityDeltaQueryJsonData" data-toggle="collapse" data-target=".method_setEntityDeltaQueryJsonData .collapse">
                            <h2>setEntityDeltaQueryJsonData</h2>
                            <pre>setEntityDeltaQueryJsonData( $entity) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setEntityMetadata"></a>
                        <div class="element clickable method public  method_setEntityMetadata" data-toggle="collapse" data-target=".method_setEntityMetadata .collapse">
                            <h2>setEntityMetadata</h2>
                            <pre>setEntityMetadata( $entity) : mixed</pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                            <h3>Response</h3>
                                        <code>mixed</code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setEntityQueryJsonData"></a>
                        <div class="element clickable method public  method_setEntityQueryJsonData" data-toggle="collapse" data-target=".method_setEntityQueryJsonData .collapse">
                            <h2>setEntityQueryJsonData</h2>
                            <pre>setEntityQueryJsonData( $entity) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setEntityUrl"></a>
                        <div class="element clickable method public  method_setEntityUrl" data-toggle="collapse" data-target=".method_setEntityUrl .collapse">
                            <h2>setEntityUrl</h2>
                            <pre>setEntityUrl( $entity,  $ormMethod,  $params = array()) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                                                            <tr>
                                                <th>Throws</th>
                                                <td>
                                                    <dl>
                                                                                                            <dt>\Exception</dt>
                                                        <dd></dd>
                                                                                                                                                            </dl>
                                                </td>
                                            </tr>
                                                                            </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$ormMethod</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$params</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setQueryParams"></a>
                        <div class="element clickable method public  method_setQueryParams" data-toggle="collapse" data-target=".method_setQueryParams .collapse">
                            <h2>setQueryParams</h2>
                            <pre>setQueryParams( $entity,  $urlParams = array()) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$urlParams</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setRawQueryJsonData"></a>
                        <div class="element clickable method public  method_setRawQueryJsonData" data-toggle="collapse" data-target=".method_setRawQueryJsonData .collapse">
                            <h2>setRawQueryJsonData</h2>
                            <pre>setRawQueryJsonData( $entity,  $data) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$data</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_validateAttribute"></a>
                        <div class="element clickable method public  method_validateAttribute" data-toggle="collapse" data-target=".method_validateAttribute .collapse">
                            <h2>validateAttribute</h2>
                            <pre>validateAttribute( $entity,  $attribute, array $properties,  $value = null) </pre>
                            <div class="labels">
                                                                                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$entity</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$attribute</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$properties</h4>
                                                <code>array</code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                    
                    
                                                                <h3><i class="icon-custom icon-property"></i> Properties</h3>
                                                    <a id="property_CIPHER"> </a>
                            <div class="element clickable property  public property_CIPHER" data-toggle="collapse" data-target=".property_CIPHER .collapse">
                                <h2>CIPHER</h2>
                                <pre>CIPHER : string</pre>
                                <div class="labels">
                                                                        <span class="label">static</span>                                </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                            </div>
            </div>
            <a id="\MercadoPago\Manager"></a>
            <ul class="breadcrumb">
                <li><a href="../index.html"><i class="icon-custom icon-class"></i></a></li>
                    
    
    <li><span class="divider">\</span><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>

                <li class="active"><span class="divider">\</span><a href="../classes/MercadoPago.Manager.html">Manager</a></li>
            </ul>
        </div>
    </div>

    </div>

        <footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by
            <a href="http://glyphicons.com/">Glyphicons</a>.<br/>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor </a> and<br/>
            generated on Thu, 25 Jun 2020 12:36:20 +0000.<br/>
    </footer>
    </body>
</html>
