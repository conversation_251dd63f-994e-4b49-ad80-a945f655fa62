<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta charset="utf-8"/>
    <title>    SDK Mercado Pago &raquo; \MercadoPago\POS
</title>
    <meta name="author" content=""/>
    <meta name="description" content=""/>

            <link href="../css/template.css" rel="stylesheet" media="all"/>
    
            <!--[if lt IE 9]>
        <script src="https://html5shim.googlecode.com/svn/trunk/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script>
        <script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
        <script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script>
        <script src="../js/bootstrap.js" type="text/javascript"></script>
        <script src="../js/template.js" type="text/javascript"></script>
        <script src="../js/prettify/prettify.min.js" type="text/javascript"></script>
    
            <link rel="shortcut icon" href="../img/favicon.ico"/>
        <link rel="apple-touch-icon" href="../img/apple-touch-icon.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png"/>
    </head>
<body>

        <div class="navbar navbar-fixed-top">
        <div class="navbar-inner">
            <div class="container">
                <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span>
                    <span class="icon-bar"></span> </a>
                <a class="brand" href="../index.html">SDK Mercado Pago</a>

                <div class="nav-collapse">
                    <ul class="nav">
                        <li class="dropdown">
                            <a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                API Documentation <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                                                    <li><a>Namespaces</a></li>
                                                                        <li><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>
                                                                                                    
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/gitrepo/sdk/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                            </ul>
                        </li>
                        <li class="dropdown" id="charts-menu">
                            <a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                Charts <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../graph_class.html">
                                        <i class="icon-list-alt"></i>&#160;Class hierarchy diagram
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown" id="reports-menu">
                            <a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                Reports <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../errors.html">
                                        <i class="icon-list-alt"></i>&#160;Errors
                                    </a>
                                </li>
                                <li>
                                    <a href="../markers.html">
                                        <i class="icon-list-alt"></i>&#160;Markers
                                    </a>
                                </li>
                                <li>
                                    <a href="../deprecated.html">
                                        <i class="icon-list-alt"></i>&#160;Deprecated
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="go_to_top">
            <a href="#___" style="color: inherit">Back to top&#160;&#160;<i class="icon-upload icon-white"></i></a>
        </div>
    </div>
    
    <div id="___" class="container">
        <noscript>
            <div class="alert alert-warning">
                Javascript is disabled; several features are only available if Javascript is enabled.
            </div>
        </noscript>

        
            <style>
        .deprecated h2 {
            text-decoration: line-through;
        }
    </style>
    <div class="row">
        <div class="span4">
                    <div class="btn-group view pull-right" data-toggle="buttons-radio">
        <button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button>
        <button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
    </div>
    <div class="btn-group visibility" data-toggle="buttons-checkbox">
        <button class="btn public active" title="Show public elements">Public</button>
        <button class="btn protected" title="Show protected elements">Protected</button>
        <button class="btn private" title="Show private elements">Private</button>
        <button class="btn inherited active" title="Show inherited elements">Inherited</button>
    </div>

        <ul class="side-nav nav nav-list">
        <li class="nav-header">
            <i class="icon-custom icon-method"></i> Methods
            <ul>
                                            </ul>
        </li>
        <li class="nav-header protected">» Protected
            <ul>
                                            </ul>
        </li>
        <li class="nav-header private">» Private
            <ul>
                                            </ul>
        </li>
        <li class="nav-header">
            <i class="icon-custom icon-constant"></i> Constants
            <ul>
                            </ul>
        </li>
    </ul>


        </div>

        <div class="span8">
            <div class="element class">
                <h1>POS</h1>
                <small style="display: block; text-align: right">
                                            Extends \MercadoPago\Entity
                                                        </small>
                <p class="short_description">POS class</p>
                <div class="details">
                    <div class="long_description">
                        
                    </div>
                    <table class="table table-bordered">
                                                                                    <tr>
                                    <th>
                                        link
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>Click here for more infos</p>
                                    </td>
                                </tr>
                                                                                                                <tr>
                                    <th>
                                        RestMethod
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>(resource=&quot;/pos/:id&quot;, method=&quot;read&quot;)</p>
                                    </td>
                                </tr>
                                                            <tr>
                                    <th>
                                        RestMethod
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>(resource=&quot;/pos&quot;, method=&quot;create&quot;)</p>
                                    </td>
                                </tr>
                                                            <tr>
                                    <th>
                                        RestMethod
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>(resource=&quot;/pos/:id&quot;, method=&quot;update&quot;)</p>
                                    </td>
                                </tr>
                                                            <tr>
                                    <th>
                                        RestMethod
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>(resource=&quot;/pos/:id&quot;, method=&quot;delete&quot;)</p>
                                    </td>
                                </tr>
                                                            <tr>
                                    <th>
                                        RestMethod
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>(resource=&quot;/pos&quot;, method=&quot;search&quot;)</p>
                                    </td>
                                </tr>
                                                                                                                <tr>
                                    <th>
                                        package
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>Default</p>
                                    </td>
                                </tr>
                                                                                                                                                                                </table>

                    <h3><i class="icon-custom icon-method"></i> Methods</h3>
                                        
                    
                                                                <h3><i class="icon-custom icon-property"></i> Properties</h3>
                                                    <a id="property_id"> </a>
                            <div class="element clickable property  protected property_id" data-toggle="collapse" data-target=".property_id .collapse">
                                <h2>id</h2>
                                <pre>id : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_name"> </a>
                            <div class="element clickable property  protected property_name" data-toggle="collapse" data-target=".property_name .collapse">
                                <h2>name</h2>
                                <pre>name : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_fixed_amount"> </a>
                            <div class="element clickable property  protected property_fixed_amount" data-toggle="collapse" data-target=".property_fixed_amount .collapse">
                                <h2>fixed_amount</h2>
                                <pre>fixed_amount : float</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>float</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_category"> </a>
                            <div class="element clickable property  protected property_category" data-toggle="collapse" data-target=".property_category .collapse">
                                <h2>category</h2>
                                <pre>category : integer</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>integer</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_store_id"> </a>
                            <div class="element clickable property  protected property_store_id" data-toggle="collapse" data-target=".property_store_id .collapse">
                                <h2>store_id</h2>
                                <pre>store_id : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_external_id"> </a>
                            <div class="element clickable property  protected property_external_id" data-toggle="collapse" data-target=".property_external_id .collapse">
                                <h2>external_reference</h2>
                                <pre>external_id : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_qr"> </a>
                            <div class="element clickable property  protected property_qr" data-toggle="collapse" data-target=".property_qr .collapse">
                                <h2>qr</h2>
                                <pre>qr : object</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>object</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_status"> </a>
                            <div class="element clickable property  protected property_status" data-toggle="collapse" data-target=".property_status .collapse">
                                <h2>status</h2>
                                <pre>status : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_date_created"> </a>
                            <div class="element clickable property  protected property_date_created" data-toggle="collapse" data-target=".property_date_created .collapse">
                                <h2>date_created</h2>
                                <pre>date_created : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_date_last_updated"> </a>
                            <div class="element clickable property  protected property_date_last_updated" data-toggle="collapse" data-target=".property_date_last_updated .collapse">
                                <h2>date_last_updated</h2>
                                <pre>date_last_updated : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_uuid"> </a>
                            <div class="element clickable property  protected property_uuid" data-toggle="collapse" data-target=".property_uuid .collapse">
                                <h2>uuid</h2>
                                <pre>uuid : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_compatible_id"> </a>
                            <div class="element clickable property  protected property_compatible_id" data-toggle="collapse" data-target=".property_compatible_id .collapse">
                                <h2>compatible_id</h2>
                                <pre>compatible_id : string</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>string</code>
                                                                            </div>
                                </div>
                            </div>
                                                    <a id="property_user_id"> </a>
                            <div class="element clickable property  protected property_user_id" data-toggle="collapse" data-target=".property_user_id .collapse">
                                <h2>user_id</h2>
                                <pre>user_id : integer</pre>
                                <div class="labels">
                                                                                                        </div>
                                <div class="row collapse">
                                    <div class="detail-description">
                                        <div class="long_description"></div>

                                        <table class="table">
                                                                                            <tr>
                                                    <th>
                                                        Attribute
                                                    </th>
                                                    <td>
                                                                                                                    <p>()</p>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <th>
                                                        var
                                                    </th>
                                                    <td>
                                                                                                                    
                                                                                                            </td>
                                                </tr>
                                                                                    </table>

                                                                                    <h3>Type(s)</h3>
                                            <code>integer</code>
                                                                            </div>
                                </div>
                            </div>
                                                            </div>
            </div>
            <a id="\MercadoPago\POS"></a>
            <ul class="breadcrumb">
                <li><a href="../index.html"><i class="icon-custom icon-class"></i></a></li>
                    
    
    <li><span class="divider">\</span><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>

                <li class="active"><span class="divider">\</span><a href="../classes/MercadoPago.POS.html">POS</a></li>
            </ul>
        </div>
    </div>

    </div>

        <footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by
            <a href="http://glyphicons.com/">Glyphicons</a>.<br/>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor </a> and<br/>
            generated on Wed, 30 Dec 2020 18:53:39 +0000.<br/>
    </footer>
    </body>
</html>
