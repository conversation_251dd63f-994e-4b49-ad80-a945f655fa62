<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <meta charset="utf-8"/>
    <title>    SDK Mercado Pago &raquo; \MercadoPago\SDK
</title>
    <meta name="author" content=""/>
    <meta name="description" content=""/>

            <link href="../css/template.css" rel="stylesheet" media="all"/>
    
            <!--[if lt IE 9]>
        <script src="https://html5shim.googlecode.com/svn/trunk/html5.js" type="text/javascript"></script>
        <![endif]-->
        <script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script>
        <script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
        <script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script>
        <script src="../js/bootstrap.js" type="text/javascript"></script>
        <script src="../js/template.js" type="text/javascript"></script>
        <script src="../js/prettify/prettify.min.js" type="text/javascript"></script>
    
            <link rel="shortcut icon" href="../img/favicon.ico"/>
        <link rel="apple-touch-icon" href="../img/apple-touch-icon.png"/>
        <link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png"/>
        <link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png"/>
    </head>
<body>

        <div class="navbar navbar-fixed-top">
        <div class="navbar-inner">
            <div class="container">
                <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span> <span class="icon-bar"></span>
                    <span class="icon-bar"></span> </a>
                <a class="brand" href="../index.html">SDK Mercado Pago</a>

                <div class="nav-collapse">
                    <ul class="nav">
                        <li class="dropdown">
                            <a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                API Documentation <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                                                    <li><a>Namespaces</a></li>
                                                                        <li><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>
                                                                                                    
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                            </ul>
                        </li>
                        <li class="dropdown" id="charts-menu">
                            <a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                Charts <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../graph_class.html">
                                        <i class="icon-list-alt"></i>&#160;Class hierarchy diagram
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown" id="reports-menu">
                            <a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                Reports <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a href="../errors.html">
                                        <i class="icon-list-alt"></i>&#160;Errors
                                    </a>
                                </li>
                                <li>
                                    <a href="../markers.html">
                                        <i class="icon-list-alt"></i>&#160;Markers
                                    </a>
                                </li>
                                <li>
                                    <a href="../deprecated.html">
                                        <i class="icon-list-alt"></i>&#160;Deprecated
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="go_to_top">
            <a href="#___" style="color: inherit">Back to top&#160;&#160;<i class="icon-upload icon-white"></i></a>
        </div>
    </div>
    
    <div id="___" class="container">
        <noscript>
            <div class="alert alert-warning">
                Javascript is disabled; several features are only available if Javascript is enabled.
            </div>
        </noscript>

        
            <style>
        .deprecated h2 {
            text-decoration: line-through;
        }
    </style>
    <div class="row">
        <div class="span4">
                    <div class="btn-group view pull-right" data-toggle="buttons-radio">
        <button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button>
        <button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
    </div>
    <div class="btn-group visibility" data-toggle="buttons-checkbox">
        <button class="btn public active" title="Show public elements">Public</button>
        <button class="btn protected" title="Show protected elements">Protected</button>
        <button class="btn private" title="Show private elements">Private</button>
        <button class="btn inherited active" title="Show inherited elements">Inherited</button>
    </div>

        <ul class="side-nav nav nav-list">
        <li class="nav-header">
            <i class="icon-custom icon-method"></i> Methods
            <ul>
                                                                                                    <li class="method public">
        <a href="#method_addCustomTrackingParam" title="addCustomTrackingParam :: ">
            <span class="description"></span><pre>addCustomTrackingParam</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_cleanCredentials" title="cleanCredentials :: ">
            <span class="description"></span><pre>cleanCredentials</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_config" title="config :: ">
            <span class="description"></span><pre>config</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_configure" title="configure :: ">
            <span class="description"></span><pre>configure</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_delete" title="delete :: ">
            <span class="description"></span><pre>delete</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_get" title="get :: ">
            <span class="description"></span><pre>get</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getAccessToken" title="getAccessToken :: ">
            <span class="description"></span><pre>getAccessToken</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getClientId" title="getClientId :: ">
            <span class="description"></span><pre>getClientId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getClientSecret" title="getClientSecret :: ">
            <span class="description"></span><pre>getClientSecret</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getCorporationId" title="getCorporationId :: ">
            <span class="description"></span><pre>getCorporationId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getCountryId" title="getCountryId :: ">
            <span class="description"></span><pre>getCountryId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getIntegratorId" title="getIntegratorId :: ">
            <span class="description"></span><pre>getIntegratorId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getPlatformId" title="getPlatformId :: ">
            <span class="description"></span><pre>getPlatformId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_getPublicKey" title="getPublicKey :: ">
            <span class="description"></span><pre>getPublicKey</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_initialize" title="initialize :: MercadoPagoSdk constructor.">
            <span class="description">MercadoPagoSdk constructor.</span><pre>initialize</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_post" title="post :: ">
            <span class="description"></span><pre>post</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_put" title="put :: ">
            <span class="description"></span><pre>put</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setAccessToken" title="setAccessToken :: Set Access Token for SDK .">
            <span class="description">Set Access Token for SDK .</span><pre>setAccessToken</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setClientId" title="setClientId :: Set Access ClientId for SDK .">
            <span class="description">Set Access ClientId for SDK .</span><pre>setClientId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setClientSecret" title="setClientSecret :: Set Access ClientSecret for SDK .">
            <span class="description">Set Access ClientSecret for SDK .</span><pre>setClientSecret</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setCorporationId" title="setCorporationId :: Set Corporation Id for SDK .">
            <span class="description">Set Corporation Id for SDK .</span><pre>setCorporationId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setIntegratorId" title="setIntegratorId :: Set Integrator Id for SDK .">
            <span class="description">Set Integrator Id for SDK .</span><pre>setIntegratorId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setMultipleCredentials" title="setMultipleCredentials :: ">
            <span class="description"></span><pre>setMultipleCredentials</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setPlatformId" title="setPlatformId :: Set Platform Id for SDK .">
            <span class="description">Set Platform Id for SDK .</span><pre>setPlatformId</pre>
        </a>
    </li>

                                                                                                        <li class="method public">
        <a href="#method_setPublicKey" title="setPublicKey :: Set Access ClientSecret for SDK .">
            <span class="description">Set Access ClientSecret for SDK .</span><pre>setPublicKey</pre>
        </a>
    </li>

                                                </ul>
        </li>
        <li class="nav-header protected">» Protected
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </ul>
        </li>
        <li class="nav-header private">» Private
            <ul>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                </ul>
        </li>
        <li class="nav-header">
            <i class="icon-custom icon-constant"></i> Constants
            <ul>
                            </ul>
        </li>
    </ul>


        </div>

        <div class="span8">
            <div class="element class">
                <h1>SDK</h1>
                <small style="display: block; text-align: right">
                                                        </small>
                <p class="short_description">MercadoPagoSdk Class Doc Comment</p>
                <div class="details">
                    <div class="long_description">
                        
                    </div>
                    <table class="table table-bordered">
                                                                                    <tr>
                                    <th>
                                        package
                                                                            </th>
                                    <td>
                                                                                    
                                                                                <p>MercadoPago</p>
                                    </td>
                                </tr>
                                                                                                                                                                                </table>

                    <h3><i class="icon-custom icon-method"></i> Methods</h3>
                                                                <a id="method_addCustomTrackingParam"></a>
                        <div class="element clickable method public  method_addCustomTrackingParam" data-toggle="collapse" data-target=".method_addCustomTrackingParam .collapse">
                            <h2>addCustomTrackingParam</h2>
                            <pre>addCustomTrackingParam( $key,  $value) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$key</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$value</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_cleanCredentials"></a>
                        <div class="element clickable method public  method_cleanCredentials" data-toggle="collapse" data-target=".method_cleanCredentials .collapse">
                            <h2>cleanCredentials</h2>
                            <pre>cleanCredentials() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_config"></a>
                        <div class="element clickable method public  method_config" data-toggle="collapse" data-target=".method_config .collapse">
                            <h2>config</h2>
                            <pre>config() : \MercadoPago\Config</pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                            <h3>Response</h3>
                                        <code><a href="../classes/MercadoPago.Config.html">\MercadoPago\Config</a></code><p></p>
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_configure"></a>
                        <div class="element clickable method public  method_configure" data-toggle="collapse" data-target=".method_configure .collapse">
                            <h2>configure</h2>
                            <pre>configure( $data = array()) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$data</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_delete"></a>
                        <div class="element clickable method public  method_delete" data-toggle="collapse" data-target=".method_delete .collapse">
                            <h2>delete</h2>
                            <pre>delete( $uri,  $options = array()) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$uri</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_get"></a>
                        <div class="element clickable method public  method_get" data-toggle="collapse" data-target=".method_get .collapse">
                            <h2>get</h2>
                            <pre>get( $uri,  $options = array()) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$uri</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getAccessToken"></a>
                        <div class="element clickable method public  method_getAccessToken" data-toggle="collapse" data-target=".method_getAccessToken .collapse">
                            <h2>getAccessToken</h2>
                            <pre>getAccessToken() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getClientId"></a>
                        <div class="element clickable method public  method_getClientId" data-toggle="collapse" data-target=".method_getClientId .collapse">
                            <h2>getClientId</h2>
                            <pre>getClientId() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getClientSecret"></a>
                        <div class="element clickable method public  method_getClientSecret" data-toggle="collapse" data-target=".method_getClientSecret .collapse">
                            <h2>getClientSecret</h2>
                            <pre>getClientSecret() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getCorporationId"></a>
                        <div class="element clickable method public  method_getCorporationId" data-toggle="collapse" data-target=".method_getCorporationId .collapse">
                            <h2>getCorporationId</h2>
                            <pre>getCorporationId() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getCountryId"></a>
                        <div class="element clickable method public  method_getCountryId" data-toggle="collapse" data-target=".method_getCountryId .collapse">
                            <h2>getCountryId</h2>
                            <pre>getCountryId() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getIntegratorId"></a>
                        <div class="element clickable method public  method_getIntegratorId" data-toggle="collapse" data-target=".method_getIntegratorId .collapse">
                            <h2>getIntegratorId</h2>
                            <pre>getIntegratorId() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getPlatformId"></a>
                        <div class="element clickable method public  method_getPlatformId" data-toggle="collapse" data-target=".method_getPlatformId .collapse">
                            <h2>getPlatformId</h2>
                            <pre>getPlatformId() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_getPublicKey"></a>
                        <div class="element clickable method public  method_getPublicKey" data-toggle="collapse" data-target=".method_getPublicKey .collapse">
                            <h2>getPublicKey</h2>
                            <pre>getPublicKey() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_initialize"></a>
                        <div class="element clickable method public  method_initialize" data-toggle="collapse" data-target=".method_initialize .collapse">
                            <h2>MercadoPagoSdk constructor.</h2>
                            <pre>initialize() </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                    
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_post"></a>
                        <div class="element clickable method public  method_post" data-toggle="collapse" data-target=".method_post .collapse">
                            <h2>post</h2>
                            <pre>post( $uri,  $options = array()) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$uri</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_put"></a>
                        <div class="element clickable method public  method_put" data-toggle="collapse" data-target=".method_put .collapse">
                            <h2>put</h2>
                            <pre>put( $uri,  $options = array()) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$uri</h4>
                                                <code></code><p></p>
                                            </div>
                                                                                    <div class="subelement argument">
                                                <h4>$options</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setAccessToken"></a>
                        <div class="element clickable method public  method_setAccessToken" data-toggle="collapse" data-target=".method_setAccessToken .collapse">
                            <h2>Set Access Token for SDK .</h2>
                            <pre>setAccessToken( $access_token) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$access_token</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setClientId"></a>
                        <div class="element clickable method public  method_setClientId" data-toggle="collapse" data-target=".method_setClientId .collapse">
                            <h2>Set Access ClientId for SDK .</h2>
                            <pre>setClientId( $client_id) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$client_id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setClientSecret"></a>
                        <div class="element clickable method public  method_setClientSecret" data-toggle="collapse" data-target=".method_setClientSecret .collapse">
                            <h2>Set Access ClientSecret for SDK .</h2>
                            <pre>setClientSecret( $client_secret) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$client_secret</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setCorporationId"></a>
                        <div class="element clickable method public  method_setCorporationId" data-toggle="collapse" data-target=".method_setCorporationId .collapse">
                            <h2>Set Corporation Id for SDK .</h2>
                            <pre>setCorporationId( $corporation_id) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$corporation_id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setIntegratorId"></a>
                        <div class="element clickable method public  method_setIntegratorId" data-toggle="collapse" data-target=".method_setIntegratorId .collapse">
                            <h2>Set Integrator Id for SDK .</h2>
                            <pre>setIntegratorId( $integrator_id) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$integrator_id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setMultipleCredentials"></a>
                        <div class="element clickable method public  method_setMultipleCredentials" data-toggle="collapse" data-target=".method_setMultipleCredentials .collapse">
                            <h2>setMultipleCredentials</h2>
                            <pre>setMultipleCredentials( $array) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$array</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setPlatformId"></a>
                        <div class="element clickable method public  method_setPlatformId" data-toggle="collapse" data-target=".method_setPlatformId .collapse">
                            <h2>Set Platform Id for SDK .</h2>
                            <pre>setPlatformId( $platform_id) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$platform_id</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                                            <a id="method_setPublicKey"></a>
                        <div class="element clickable method public  method_setPublicKey" data-toggle="collapse" data-target=".method_setPublicKey .collapse">
                            <h2>Set Access ClientSecret for SDK .</h2>
                            <pre>setPublicKey( $public_key) </pre>
                            <div class="labels">
                                                                <span class="label">static</span>                                                                                                                            </div>

                            
                            <div class="row collapse">
                                <div class="detail-description">
                                    <div class="long_description"></div>

                                    <table class="table">
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                    <tr>
                                                <th>
                                                    
                                                </th>
                                                <td>
                                                                                                    </td>
                                            </tr>
                                                                                
Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293

Warning: count(): Parameter must be an array or an object that implements Countable in phar:///Users/<USER>/git/mercadopago/dx-php/phpDocumentor.phar/vendor/twig/twig/lib/Twig/Extension/Core.php on line 1293
                                    </table>

                                                                            <h3>Arguments</h3>
                                                                                    <div class="subelement argument">
                                                <h4>$public_key</h4>
                                                <code></code><p></p>
                                            </div>
                                                                            
                                                                    </div>
                            </div>

                        </div>
                    
                    
                                                        </div>
            </div>
            <a id="\MercadoPago\SDK"></a>
            <ul class="breadcrumb">
                <li><a href="../index.html"><i class="icon-custom icon-class"></i></a></li>
                    
    
    <li><span class="divider">\</span><a href="../namespaces/MercadoPago.html">MercadoPago</a></li>

                <li class="active"><span class="divider">\</span><a href="../classes/MercadoPago.SDK.html">SDK</a></li>
            </ul>
        </div>
    </div>

    </div>

        <footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by
            <a href="http://glyphicons.com/">Glyphicons</a>.<br/>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor </a> and<br/>
            generated on Thu, 25 Jun 2020 12:36:19 +0000.<br/>
    </footer>
    </body>
</html>
