<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Documentation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/js/all.min.js" integrity="sha256-0vuk8LXoyrmCjp1f0O300qo1M75ZQyhH9X3J6d+scmk=" crossorigin="anonymous"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -four phpdocumentor-sidebar">
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                <h3 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/default.html"><abbr title="\">Global</abbr></a></h3>
                                        <h4 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/mercadopago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h4>
                <ul class="phpdocumentor-list">
                                            <li><a href="namespaces/mercadopago-annotation.html"><abbr title="\MercadoPago\Annotation">Annotation</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-advancedpayments.html"><abbr title="\MercadoPago\AdvancedPayments">AdvancedPayments</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                                    </ul>
                        </section>

        <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                    <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/Default.html"><abbr title="\Default">Default</abbr></a></h3>
                                <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/MercadoPago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h3>
                        <ul class="phpdocumentor-list">
                                    <li><a href="packages/MercadoPago-Config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                    <li><a href="packages/MercadoPago-Http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                            </ul>
                        </section>
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -eight phpdocumentor-content">
                    <ul class="phpdocumentor-breadcrumbs">
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    PaymentTest

        <span class="phpdocumentor-element__extends">
        extends <abbr title="\PHPUnit\Framework\TestCase">TestCase</abbr>
    </span>
    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/MercadoPago.html">MercadoPago</a></li>
                            </ul>
        </div>
    
    
    </h2>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">10</span>
</aside>

            <p class="phpdocumentor-summary">EntityTest Class Doc Comment</p>

    <section class="phpdocumentor-description"></section>








<h3 id="toc">
    Table of Contents
    <a href="#toc" class="headerlink"><i class="fas fa-link"></i></a>
</h3>

<dl class="phpdocumentor-table-of-contents">
                    <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_setUpBeforeClass">setUpBeforeClass()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testCancelPayment">testCancelPayment()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testCreateAnInvalidPayment">testCreateAnInvalidPayment()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testCreateApprovedPayment">testCreateApprovedPayment()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testCreatePendingPayment">testCreatePendingPayment()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testFindPaymentById">testFindPaymentById()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testFindPaymentByNonExistentId">testFindPaymentByNonExistentId()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testPaymentsSearch">testPaymentsSearch()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testPaymentWithCustomAccessToken">testPaymentWithCustomAccessToken()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testRefundPayment">testRefundPayment()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/PaymentTest.html#method_testSearchWithInvalidQueryFilters">testSearchWithInvalidQueryFilters()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

            <dt class="phpdocumentor-table-of-contents__entry -method -private">
    <a href="classes/PaymentTest.html#method_SingleUseCardToken">SingleUseCardToken()</a>
    <span>
                        &nbsp;: mixed    </span>
</dt>
<dd></dd>

        </dl>



        

        

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/PaymentTest.html#methods" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_setUpBeforeClass">
        setUpBeforeClass()
        <a href="classes/PaymentTest.html#method_setUpBeforeClass" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">13</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>    <span class="phpdocumentor-signature__name">setUpBeforeClass</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testCancelPayment">
        testCancelPayment()
        <a href="classes/PaymentTest.html#method_testCancelPayment" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">158</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testCancelPayment</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$payment_created_previously</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$payment_created_previously</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">depends</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>testCreatePendingPayment</p>
</section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testCreateAnInvalidPayment">
        testCreateAnInvalidPayment()
        <a href="classes/PaymentTest.html#method_testCreateAnInvalidPayment" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">73</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testCreateAnInvalidPayment</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testCreateApprovedPayment">
        testCreateApprovedPayment()
        <a href="classes/PaymentTest.html#method_testCreateApprovedPayment" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">31</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testCreateApprovedPayment</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testCreatePendingPayment">
        testCreatePendingPayment()
        <a href="classes/PaymentTest.html#method_testCreatePendingPayment" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">101</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testCreatePendingPayment</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testFindPaymentById">
        testFindPaymentById()
        <a href="classes/PaymentTest.html#method_testFindPaymentById" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">125</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testFindPaymentById</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$payment_created_previously</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$payment_created_previously</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">depends</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>testCreatePendingPayment</p>
</section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testFindPaymentByNonExistentId">
        testFindPaymentByNonExistentId()
        <a href="classes/PaymentTest.html#method_testFindPaymentByNonExistentId" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">133</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testFindPaymentByNonExistentId</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$payment_created_previously</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$payment_created_previously</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">depends</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>testCreatePendingPayment</p>
</section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testPaymentsSearch">
        testPaymentsSearch()
        <a href="classes/PaymentTest.html#method_testPaymentsSearch" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">141</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testPaymentsSearch</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$payment_created_previously</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$payment_created_previously</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">depends</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>testCreatePendingPayment</p>
</section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testPaymentWithCustomAccessToken">
        testPaymentWithCustomAccessToken()
        <a href="classes/PaymentTest.html#method_testPaymentWithCustomAccessToken" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">169</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testPaymentWithCustomAccessToken</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testRefundPayment">
        testRefundPayment()
        <a href="classes/PaymentTest.html#method_testRefundPayment" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">56</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testRefundPayment</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a>&nbsp;</span><span class="phpdocumentor-signature__argument__name">$payment_created_previously</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$payment_created_previously</span>
                : <span class="phpdocumentor-signature__argument__return-type"><a href="classes/MercadoPago-Payment.html"><abbr title="\MercadoPago\Payment">Payment</abbr></a></span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    
    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">depends</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>testCreateApprovedPayment</p>
</section>

                                    </dd>
                        </dl>

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_testSearchWithInvalidQueryFilters">
        testSearchWithInvalidQueryFilters()
        <a href="classes/PaymentTest.html#method_testSearchWithInvalidQueryFilters" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">86</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
                <span class="phpdocumentor-signature__name">testSearchWithInvalidQueryFilters</span><span>(</span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
    
    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
                    <article
        class="phpdocumentor-element
            -method
            -private
                                                        "
>
    <h4 class="phpdocumentor-element__name" id="method_SingleUseCardToken">
        SingleUseCardToken()
        <a href="classes/PaymentTest.html#method_SingleUseCardToken" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="tests/resources/PaymentTest.php"><a href="files/tests-resources-paymenttest.html"><abbr title="tests/resources/PaymentTest.php">PaymentTest.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">188</span>
</aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">private</span>
                <span class="phpdocumentor-signature__name">SingleUseCardToken</span><span>(</span><span class="phpdocumentor-signature__argument"><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$status</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

    
        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$status</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                
            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
            <section class="phpdocumentor-description"></section>

    
</article>
            </section>

    </article>
                <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
            </div>
        </div>
        <a href="classes/PaymentTest.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
</body>
</html>
