<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Documentation</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/js/all.min.js" integrity="sha256-0vuk8LXoyrmCjp1f0O300qo1M75ZQyhH9X3J6d+scmk=" crossorigin="anonymous"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -four phpdocumentor-sidebar">
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                <h3 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/default.html"><abbr title="\">Global</abbr></a></h3>
                                        <h4 class="phpdocumentor-sidebar__root-namespace"><a href="namespaces/mercadopago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h4>
                <ul class="phpdocumentor-list">
                                            <li><a href="namespaces/mercadopago-annotation.html"><abbr title="\MercadoPago\Annotation">Annotation</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-advancedpayments.html"><abbr title="\MercadoPago\AdvancedPayments">AdvancedPayments</abbr></a></li>
                                            <li><a href="namespaces/mercadopago-http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                                    </ul>
                        </section>

        <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                    <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/Default.html"><abbr title="\Default">Default</abbr></a></h3>
                                <h3 class="phpdocumentor-sidebar__root-package"><a href="packages/MercadoPago.html"><abbr title="\MercadoPago">MercadoPago</abbr></a></h3>
                        <ul class="phpdocumentor-list">
                                    <li><a href="packages/MercadoPago-Config.html"><abbr title="\MercadoPago\Config">Config</abbr></a></li>
                                    <li><a href="packages/MercadoPago-Http.html"><abbr title="\MercadoPago\Http">Http</abbr></a></li>
                            </ul>
                        </section>
    
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -eight phpdocumentor-content">
                    <ul class="phpdocumentor-breadcrumbs">
    </ul>

    <article class="phpdocumentor-element -file">
        <h2 class="phpdocumentor-content__title">DenyDynamicAttribute.php</h2>

        

<h3 id="interfaces_class_traits">
    Interfaces, Classes and Traits
    <a href="#interfaces_class_traits" class="headerlink"><i class="fas fa-link"></i></a>
</h3>

<dl class="phpdocumentor-table-of-contents">
    
            <dt class="phpdocumentor-table-of-contents__entry -class"><a href="classes/MercadoPago-Annotation-DenyDynamicAttribute.html"><abbr title="\MercadoPago\Annotation\DenyDynamicAttribute">DenyDynamicAttribute</abbr></a></dt>
        <dd></dd>
    
    </dl>




        

        
    </article>
                <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
            </div>
        </div>
        <a href="files/src-mercadopago-annotation-denydynamicattribute.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
</body>
</html>
